package xiaozhi.modules.sys.controller;

import java.util.List;
import java.util.Set;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.tenant.TenantContext;
import xiaozhi.modules.sys.dto.UserCreateRequestDTO;
import xiaozhi.modules.sys.dto.UserDetailResponseDTO;
import xiaozhi.modules.sys.dto.UserQueryRequestDTO;
import xiaozhi.modules.sys.dto.UserUpdateRequestDTO;
import xiaozhi.modules.sys.entity.SysRoleEntity;
import xiaozhi.modules.sys.service.SysPermissionService;
import xiaozhi.modules.sys.service.SysRoleService;
import xiaozhi.modules.sys.service.SysUserRoleService;
import xiaozhi.modules.sys.service.SysUserService;

/**
 * 用户管理控制层
 *
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/user")
@Tag(name = "用户管理", description = "统一的用户管理功能，支持多租户数据隔离")
public class UserController {
    private final SysUserService sysUserService;
    private final SysUserRoleService sysUserRoleService;
    private final SysRoleService sysRoleService;
    private final SysPermissionService sysPermissionService;

    @GetMapping
    @Operation(
        summary = "分页查询用户",
        description = "分页查询用户列表，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<PageData<UserDetailResponseDTO>> page(UserQueryRequestDTO queryRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(queryRequest);

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        PageData<UserDetailResponseDTO> pageData = sysUserService.pageWithDetails(queryRequest);
        return new Result<PageData<UserDetailResponseDTO>>().ok(pageData);
    }

    @GetMapping("/list")
    @Operation(
        summary = "获取用户列表",
        description = "获取用户列表，不分页，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<List<UserDetailResponseDTO>> list() {
        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<UserDetailResponseDTO> userList = sysUserService.getAllUsers();
        return new Result<List<UserDetailResponseDTO>>().ok(userList);
    }

    @GetMapping("/{userId}")
    @Operation(
        summary = "获取用户详情",
        description = "根据用户ID获取用户详细信息，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<UserDetailResponseDTO> info(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        // 如果用户不在当前租户，拦截器会自动过滤，返回null
        UserDetailResponseDTO user = sysUserService.getUserDetail(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在或无权限访问");
        }

        return new Result<UserDetailResponseDTO>().ok(user);
    }

    @PostMapping
    @Operation(
        summary = "创建用户",
        description = "创建新用户，自动设置租户信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数验证失败")
    })
    @RequiresPermission(value = "user:create:*", description = "创建用户权限")
    public Result<Void> create(@RequestBody UserCreateRequestDTO createRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(createRequest);

        // 权限检查：非平台管理员不能创建平台管理员
        if (!TenantContext.isPlatformAdmin() && createRequest.getUserType() == 1) {
            throw new RuntimeException("无权限创建平台管理员");
        }

        sysUserService.createUser(createRequest);

        return new Result<>();
    }

    @PutMapping
    @Operation(
        summary = "更新用户",
        description = "更新用户信息，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:*", description = "编辑用户权限")
    public Result<Void> update(@RequestBody UserUpdateRequestDTO updateRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(updateRequest);

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        // 如果用户不在当前租户，更新操作会被拦截器阻止
        sysUserService.updateUser(updateRequest);

        return new Result<>();
    }

    @DeleteMapping("/{userId}")
    @Operation(
        summary = "删除用户",
        description = "删除指定用户，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:delete:*", description = "删除用户权限")
    public Result<Void> delete(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        // 如果用户不在当前租户，删除操作会被拦截器阻止
        sysUserService.deleteUser(userId);

        return new Result<>();
    }

    @PutMapping("/{userId}/status")
    @Operation(
        summary = "修改用户状态",
        description = "启用或禁用用户，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "修改成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:*", description = "编辑用户权限")
    public Result<Void> updateStatus(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId,
            @Parameter(description = "状态", required = true, example = "1")
            @RequestParam Integer status) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        sysUserService.updateUserStatus(userId, status);

        return new Result<>();
    }

    @PutMapping("/{userId}/reset-password")
    @Operation(
        summary = "重置用户密码",
        description = "重置用户密码为默认密码，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重置成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:*", description = "编辑用户权限")
    public Result<String> resetPassword(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        String newPassword = sysUserService.resetPassword(userId);

        return new Result<String>().ok(newPassword);
    }

    @GetMapping("/{userId}/roles")
    @Operation(
        summary = "获取用户角色列表",
        description = "获取指定用户的所有角色，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<List<SysRoleEntity>> getUserRoles(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<Long> roleIds = sysUserRoleService.getRoleIdsByUserId(userId);
        List<SysRoleEntity> roles = roleIds.stream()
            .map(roleId -> sysRoleService.getById(roleId))
            .filter(role -> role != null)
            .toList();

        return new Result<List<SysRoleEntity>>().ok(roles);
    }

    @GetMapping("/{userId}/permissions")
    @Operation(
        summary = "获取用户权限列表",
        description = "获取指定用户的所有权限，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:*", description = "查看用户权限")
    public Result<Set<String>> getUserPermissions(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        Set<String> permissions = sysPermissionService.getUserPermissions(userId);

        return new Result<Set<String>>().ok(permissions);
    }
}
