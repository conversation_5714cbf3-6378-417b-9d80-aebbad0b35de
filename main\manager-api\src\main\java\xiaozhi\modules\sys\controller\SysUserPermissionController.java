package xiaozhi.modules.sys.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.security.utils.PermissionUtils;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.entity.SysRoleEntity;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.modules.sys.service.SysPermissionService;
import xiaozhi.modules.sys.service.SysRoleService;
import xiaozhi.modules.sys.service.SysUserRoleService;
import xiaozhi.modules.sys.service.SysUserService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户权限校验Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/user-permission")
@Tag(name = "用户权限校验", description = "用户权限校验相关接口，包括权限查询、角色查询、权限验证等功能")
@AllArgsConstructor
public class SysUserPermissionController {
    
    private final SysUserService sysUserService;
    private final SysPermissionService sysPermissionService;
    private final SysRoleService sysRoleService;
    private final SysUserRoleService sysUserRoleService;
    private final PermissionUtils permissionUtils;
    
    @GetMapping("/current/info")
    @Operation(
        summary = "获取当前用户完整权限信息",
        description = "获取当前登录用户的详细权限信息，包括用户信息、角色列表、权限列表等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    public Result<Map<String, Object>> getCurrentUserPermissionInfo() {
        Long userId = SecurityUser.getUserId();
        Long tenantId = SecurityUser.getTenantId();
        
        // 获取用户信息
        SysUserDTO user = sysUserService.get(userId);
        
        // 获取用户角色
        List<Long> roleIds = sysUserRoleService.getRoleIdsByUserId(userId);
        List<SysRoleEntity> roles = roleIds.stream()
            .map(roleId -> sysRoleService.getById(roleId))
            .filter(role -> role != null)
            .toList();
        
        // 获取用户权限
        Set<String> permissions = sysPermissionService.getUserPermissions(userId);
        
        // 权限分析
        Map<String, Object> permissionAnalysis = Map.of(
            "isPlatformAdmin", permissionUtils.isPlatformAdmin(),
            "isTenantAdmin", permissionUtils.isTenantAdmin(),
            "hasSystemConfig", permissionUtils.hasPermission("system:config:*"),
            "hasUserManage", permissionUtils.hasTenantPermission("user:create:tenant"),
            "hasDeviceManage", permissionUtils.hasTenantPermission("device:bind:tenant"),
            "hasReportView", permissionUtils.hasTenantPermission("report:view:tenant")
        );
        
        Map<String, Object> result = Map.of(
            "user", user,
            "roles", roles,
            "permissions", permissions,
            "permissionAnalysis", permissionAnalysis,
            "tenantId", tenantId,
            "userType", user.getUserType()
        );
        
        return new Result<Map<String, Object>>().ok(result);
    }
    
    @GetMapping("/user/{userId}/permissions")
    @Operation(
        summary = "获取指定用户权限列表",
        description = "获取指定用户的所有权限列表，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "查看用户权限")
    public Result<Set<String>> getUserPermissions(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        Set<String> permissions = sysPermissionService.getUserPermissions(userId);
        return new Result<Set<String>>().ok(permissions);
    }
    
    @GetMapping("/user/{userId}/roles")
    @Operation(
        summary = "获取指定用户角色列表",
        description = "获取指定用户的所有角色列表，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "查看用户角色")
    public Result<List<SysRoleEntity>> getUserRoles(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<Long> roleIds = sysUserRoleService.getRoleIdsByUserId(userId);
        List<SysRoleEntity> roles = roleIds.stream()
            .map(roleId -> sysRoleService.getById(roleId))
            .filter(role -> role != null)
            .toList();

        return new Result<List<SysRoleEntity>>().ok(roles);
    }
    
    @GetMapping("/check/{userId}/{permission}")
    @Operation(
        summary = "检查用户权限",
        description = "检查指定用户是否拥有指定权限，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "检查完成"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "检查用户权限")
    public Result<Boolean> checkUserPermission(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId,
            @Parameter(description = "权限字符串", required = true, example = "user:view:*")
            @PathVariable String permission) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        boolean hasPermission = sysPermissionService.hasPermission(userId, permission);
        return new Result<Boolean>().ok(hasPermission);
    }
    
    @GetMapping("/available-permissions")
    @Operation(
        summary = "获取租户所有用户权限概览",
        description = "获取当前租户下所有用户的权限概览信息，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "查看租户用户权限")
    public Result<List<Map<String, Object>>> getAvailablePermissions() {
        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<SysUserEntity> users = sysUserService.list(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysUserEntity>()
                .eq("deleted", 0)
        );

        List<Map<String, Object>> result = users.stream()
            .map(user -> {
                Set<String> permissions = sysPermissionService.getUserPermissions(user.getId());
                List<Long> roleIds = sysUserRoleService.getRoleIdsByUserId(user.getId());

                return Map.of(
                    "userId", user.getId(),
                    "username", user.getUsername(),
                    "realName", user.getRealName() != null ? user.getRealName() : "",
                    "status", user.getStatus(),
                    "roleCount", roleIds.size(),
                    "permissionCount", permissions.size(),
                    "permissions", permissions
                );
            })
            .toList();

        return new Result<List<Map<String, Object>>>().ok(result);
    }

    @GetMapping("/permission/analysis")
    @Operation(
        summary = "权限分析报告",
        description = "生成当前租户的权限分析报告，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "生成成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "report:view:tenant", tenant = true, description = "查看权限报告")
    public Result<Map<String, Object>> getPermissionAnalysis() {
        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        long userCount = sysUserService.count(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysUserEntity>()
                .eq("deleted", 0)
        );

        // 统计租户角色数
        List<SysRoleEntity> roles = sysRoleService.list();

        // 统计权限使用情况
        Map<String, Long> permissionStats = Map.of(
            "totalUsers", userCount,
            "totalRoles", (long) roles.size(),
            "activeUsers", sysUserService.count(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysUserEntity>()
                    .eq("status", 1)
                    .eq("deleted", 0)
            ),
            "adminUsers", sysUserService.count(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysUserEntity>()
                    .eq("user_type", 1)
                    .eq("deleted", 0)
            )
        );

        Map<String, Object> result = Map.of(
            "statistics", permissionStats,
            "roles", roles,
            "generateTime", System.currentTimeMillis()
        );

        return new Result<Map<String, Object>>().ok(result);
    }
}
