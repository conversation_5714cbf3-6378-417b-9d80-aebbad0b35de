package xiaozhi.modules.security.tenant;

/**
 * 租户上下文管理
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public class TenantContext {
    
    private static final ThreadLocal<Long> TENANT_ID_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置当前租户ID
     * 
     * @param tenantId 租户ID
     */
    public static void setTenantId(Long tenantId) {
        TENANT_ID_HOLDER.set(tenantId);
    }
    
    /**
     * 获取当前租户ID
     * 
     * @return 租户ID
     */
    public static Long getTenantId() {
        return TENANT_ID_HOLDER.get();
    }
    
    /**
     * 清除当前租户ID
     */
    public static void clear() {
        TENANT_ID_HOLDER.remove();
    }
    
    /**
     * 检查是否设置了租户ID
     * 
     * @return 是否设置了租户ID
     */
    public static boolean hasTenantId() {
        return TENANT_ID_HOLDER.get() != null;
    }
}
