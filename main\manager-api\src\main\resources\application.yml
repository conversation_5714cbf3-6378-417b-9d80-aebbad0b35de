# Undertow
server:
  undertow:
    threads:
      io: 16
      worker: 1000
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 30
  port: 8002
  servlet:
    context-path: /xiaozhi
    session:
      cookie:
        http-only: true

spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  main:
    allow-bean-definition-overriding: true

knife4j:
  enable: true
  basic:
    enable: false
    username: admin
    password: admin
  setting:
    enableFooter: false

renren:
  redis:
    open: true
  xss:
    enabled: true
    exclude-urls:

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: xiaozhi.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型
      id-type: ASSIGN_ID
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  configuration-properties:
    prefix:
    blobType: BLOB
    boolValue: TRUE