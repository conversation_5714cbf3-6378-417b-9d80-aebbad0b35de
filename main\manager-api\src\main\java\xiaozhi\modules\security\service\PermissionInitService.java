package xiaozhi.modules.security.service;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import lombok.AllArgsConstructor;
import xiaozhi.modules.sys.entity.SysPermissionEntity;
import xiaozhi.modules.sys.entity.SysRoleEntity;
import xiaozhi.modules.sys.entity.SysRolePermissionEntity;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.modules.sys.entity.SysUserRoleEntity;
import xiaozhi.modules.sys.service.SysPermissionService;
import xiaozhi.modules.sys.service.SysRoleService;
import xiaozhi.modules.sys.service.SysUserService;

/**
 * 权限初始化服务
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Service
@AllArgsConstructor
public class PermissionInitService implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(PermissionInitService.class);
    
    private final SysPermissionService sysPermissionService;
    private final SysRoleService sysRoleService;
    private final SysUserService sysUserService;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("开始初始化权限数据...");
        
        try {
            // 检查是否已经初始化过权限数据
            if (isPermissionDataInitialized()) {
                logger.info("权限数据已存在，跳过初始化");
                return;
            }
            
            // 初始化基础权限数据
            initBasicPermissions();
            
            logger.info("权限数据初始化完成");
        } catch (Exception e) {
            logger.error("权限数据初始化失败", e);
        }
    }
    
    /**
     * 检查权限数据是否已初始化
     */
    private boolean isPermissionDataInitialized() {
        QueryWrapper<SysPermissionEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("permission", "*:*:*");
        return sysPermissionService.count(wrapper) > 0;
    }
    
    /**
     * 初始化基础权限数据
     */
    private void initBasicPermissions() {
        // 创建超级管理员权限
        SysPermissionEntity superAdminPermission = new SysPermissionEntity();
        superAdminPermission.setPermission("*:*:*");
        superAdminPermission.setDescription("超级管理员权限");
        superAdminPermission.setDomain("*");
        superAdminPermission.setAction("*");
        superAdminPermission.setInstance("*");
        superAdminPermission.setStatus(1);
        superAdminPermission.setCreator(1L);
        superAdminPermission.setCreateDate(new Date());
        sysPermissionService.save(superAdminPermission);
        
        logger.info("创建超级管理员权限: {}", superAdminPermission.getPermission());
    }
}
