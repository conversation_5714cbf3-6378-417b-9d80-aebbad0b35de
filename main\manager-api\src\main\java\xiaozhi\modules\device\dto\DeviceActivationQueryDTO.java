package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import xiaozhi.common.page.Query;

/**
 * 设备激活查询DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "设备激活查询")
public class DeviceActivationQueryDTO extends Query {
    
    @Schema(description = "设备ID", example = "device123")
    private String deviceId;
    
    @Schema(description = "设备MAC地址", example = "AA:BB:CC:DD:EE:FF")
    private String macAddress;
    
    @Schema(description = "激活码", example = "ABC123")
    private String activationCode;
    
    @Schema(description = "智能体ID", example = "agent123")
    private String agentId;
    
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    
    @Schema(description = "激活状态", example = "1", allowableValues = {"0", "1", "2"})
    private Integer activationStatus;
    
    @Schema(description = "激活开始时间", example = "2025-01-01")
    private String activatedStartDate;
    
    @Schema(description = "激活结束时间", example = "2025-01-31")
    private String activatedEndDate;
    
    @Schema(description = "设备硬件型号", example = "ESP32")
    private String board;
}
