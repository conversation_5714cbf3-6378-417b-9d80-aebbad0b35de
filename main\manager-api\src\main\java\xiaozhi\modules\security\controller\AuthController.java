package xiaozhi.modules.security.controller;

import java.io.IOException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.TokenDTO;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.AssertUtils;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.security.dto.LoginDTO;
import xiaozhi.modules.security.dto.SmsVerificationDTO;
import xiaozhi.modules.security.dto.TenantLoginDTO;
import xiaozhi.modules.security.dto.TenantUserRegisterDTO;
import xiaozhi.modules.security.password.PasswordUtils;
import xiaozhi.modules.security.service.CaptchaService;
import xiaozhi.modules.security.service.SysUserTokenService;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.sys.dto.PasswordDTO;
import xiaozhi.modules.sys.dto.RetrievePasswordDTO;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.service.SysDictDataService;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.sys.service.SysUserService;
import xiaozhi.modules.sys.service.SysTenantService;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.security.tenant.TenantContext;
import xiaozhi.modules.sys.vo.SysDictDataItem;

/**
 * 身份认证控制层
 */
@AllArgsConstructor
@RestController
@RequestMapping("/auth")
@Tag(name = "身份认证", description = "用户登录、注册、信息获取等功能，支持多租户")
public class AuthController {
    private final SysUserService sysUserService;
    private final SysUserTokenService sysUserTokenService;
    private final CaptchaService captchaService;
    private final SysParamsService sysParamsService;
    private final SysDictDataService sysDictDataService;
    private final SysTenantService sysTenantService;

    @GetMapping("/captcha")
    @Operation(summary = "验证码")
    public void captcha(HttpServletResponse response, String uuid) throws IOException {
        // uuid不能为空
        AssertUtils.isBlank(uuid, ErrorCode.IDENTIFIER_NOT_NULL);
        // 生成验证码
        captchaService.create(response, uuid);
    }

    @PostMapping("/smsVerification")
    @Operation(summary = "短信验证码")
    public Result<Void> smsVerification(@RequestBody SmsVerificationDTO dto) {
        // 验证图形验证码
        boolean validate = captchaService.validate(dto.getCaptchaId(), dto.getCaptcha(), true);
        if (!validate) {
            throw new RenException("图形验证码错误");
        }
        Boolean isMobileRegister = sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class);
        if (!isMobileRegister) {
            throw new RenException("没有开启手机注册，没法使用短信验证码功能");
        }
        // 发送短信验证码
        captchaService.sendSMSValidateCode(dto.getPhone());
        return new Result<>();
    }

    @PostMapping("/login")
    @Operation(summary = "登录")
    public Result<TokenDTO> login(@RequestBody LoginDTO login) {
        // 验证是否正确输入验证码
        boolean validate = captchaService.validate(login.getCaptchaId(), login.getCaptcha(), true);
        if (!validate) {
            throw new RenException("图形验证码错误，请重新获取");
        }
        // 按照用户名获取用户
        SysUserDTO userDTO = sysUserService.getByUsername(login.getUsername());
        // 判断用户是否存在
        if (userDTO == null) {
            throw new RenException("请检测用户和密码是否输入错误");
        }
        // 判断密码是否正确，不一样则进入if
        if (!PasswordUtils.matches(login.getPassword(), userDTO.getPassword())) {
            throw new RenException("请检测用户和密码是否输入错误");
        }
        return sysUserTokenService.createToken(userDTO.getId());
    }

    @PostMapping("/register")
    @Operation(summary = "注册")
    public Result<Void> register(@RequestBody LoginDTO login) {
        if (!sysUserService.getAllowUserRegister()) {
            throw new RenException("当前不允许普通用户注册");
        }
        // 是否开启手机注册
        Boolean isMobileRegister = sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class);
        boolean validate;
        if (isMobileRegister) {
            // 验证用户是否是手机号码
            boolean validPhone = ValidatorUtils.isValidPhone(login.getUsername());
            if (!validPhone) {
                throw new RenException("用户名不是手机号码，请重新输入");
            }
            // 验证短信验证码是否正常
            validate = captchaService.validateSMSValidateCode(login.getUsername(), login.getMobileCaptcha(), false);
            if (!validate) {
                throw new RenException("手机验证码错误，请重新获取");
            }
        } else {
            // 验证是否正确输入验证码
            validate = captchaService.validate(login.getCaptchaId(), login.getCaptcha(), true);
            if (!validate) {
                throw new RenException("图形验证码错误，请重新获取");
            }
        }

        // 按照用户名获取用户
        SysUserDTO userDTO = sysUserService.getByUsername(login.getUsername());
        if (userDTO != null) {
            throw new RenException("此手机号码已经注册过");
        }
        userDTO = new SysUserDTO();
        userDTO.setUsername(login.getUsername());
        userDTO.setPassword(login.getPassword());
        sysUserService.save(userDTO);
        return new Result<>();
    }

    @GetMapping("/info")
    @Operation(summary = "用户信息获取")
    public Result<UserDetail> info() {
        UserDetail user = SecurityUser.getUser();
        Result<UserDetail> result = new Result<>();
        result.setData(user);
        return result;
    }

    @PutMapping("/change-password")
    @Operation(summary = "修改用户密码")
    public Result<?> changePassword(@RequestBody PasswordDTO passwordDTO) {
        // 判断非空
        ValidatorUtils.validateEntity(passwordDTO);
        Long userId = SecurityUser.getUserId();
        sysUserTokenService.changePassword(userId, passwordDTO);
        return new Result<>();
    }

    @PutMapping("/retrieve-password")
    @Operation(summary = "找回密码")
    public Result<?> retrievePassword(@RequestBody RetrievePasswordDTO dto) {
        // 是否开启手机注册
        Boolean isMobileRegister = sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class);
        if (!isMobileRegister) {
            throw new RenException("没有开启手机注册，没法使用找回密码功能");
        }
        // 判断非空
        ValidatorUtils.validateEntity(dto);
        // 验证用户是否是手机号码
        boolean validPhone = ValidatorUtils.isValidPhone(dto.getPhone());
        if (!validPhone) {
            throw new RenException("输入的手机号码格式不正确");
        }

        // 按照用户名获取用户
        SysUserDTO userDTO = sysUserService.getByUsername(dto.getPhone());
        if (userDTO == null) {
            throw new RenException("输入的手机号码未注册");
        }
        // 验证短信验证码是否正常
        boolean validate = captchaService.validateSMSValidateCode(dto.getPhone(), dto.getCode(), false);
        // 判断是否通过验证
        if (!validate) {
            throw new RenException("输入的手机验证码错误");
        }

        sysUserService.changePasswordDirectly(userDTO.getId(), dto.getPassword());
        return new Result<>();
    }

    @GetMapping("/pub-config")
    @Operation(summary = "公共配置")
    public Result<Map<String, Object>> pubConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enableMobileRegister", sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class));
        config.put("version", Constant.VERSION);
        config.put("year", "©" + Calendar.getInstance().get(Calendar.YEAR));
        config.put("allowUserRegister", sysUserService.getAllowUserRegister());
        List<SysDictDataItem> list = sysDictDataService.getDictDataByType(Constant.DictType.MOBILE_AREA.getValue());
        config.put("mobileAreaList", list);
        config.put("beianIcpNum", sysParamsService.getValue(Constant.SysBaseParam.BEIAN_ICP_NUM.getValue(), true));
        config.put("beianGaNum", sysParamsService.getValue(Constant.SysBaseParam.BEIAN_GA_NUM.getValue(), true));
        config.put("name", sysParamsService.getValue(Constant.SysBaseParam.SERVER_NAME.getValue(), true));

        return new Result<Map<String, Object>>().ok(config);
    }

    @PostMapping("/tenant/login")
    @Operation(
        summary = "租户登录",
        description = "支持租户的用户登录，可以指定租户编码或自动识别"
    )
    public Result<TokenDTO> tenantLogin(@RequestBody TenantLoginDTO loginDTO) {
        // 验证码校验
        if (loginDTO.getCaptchaId() != null && loginDTO.getCaptcha() != null) {
            boolean validate = captchaService.validate(loginDTO.getCaptchaId(), loginDTO.getCaptcha(), true);
            if (!validate) {
                throw new RenException("图形验证码错误，请重新获取");
            }
        }

        // 获取用户信息
        SysUserDTO userDTO = sysUserService.getByUsername(loginDTO.getUsername());
        if (userDTO == null) {
            throw new RenException("用户名或密码错误");
        }

        // 验证密码
        if (!PasswordUtils.matches(loginDTO.getPassword(), userDTO.getPassword())) {
            throw new RenException("用户名或密码错误");
        }

        // 验证租户
        if (loginDTO.getTenantCode() != null && !loginDTO.getTenantCode().isEmpty()) {
            SysTenantEntity tenant = sysTenantService.getByTenantCode(loginDTO.getTenantCode());
            if (tenant == null) {
                throw new RenException("租户不存在");
            }
            if (tenant.getStatus() != 1) {
                throw new RenException("租户已被禁用");
            }
            // 验证用户是否属于该租户
            if (!userDTO.getTenantId().equals(tenant.getId())) {
                throw new RenException("用户不属于该租户");
            }
        }

        // 设置租户上下文
        if (userDTO.getTenantId() != null) {
            TenantContext.setTenantId(userDTO.getTenantId());
        }

        return sysUserTokenService.createToken(userDTO.getId());
    }

    @PostMapping("/tenant/register")
    @Operation(
        summary = "租户用户注册",
        description = "在指定租户下注册新用户，需要租户管理员权限或邀请码"
    )
    public Result<Void> tenantRegister(@RequestBody TenantUserRegisterDTO registerDTO) {
        // 参数验证
        ValidatorUtils.validateEntity(registerDTO);

        // 密码确认
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new RenException("两次输入的密码不一致");
        }

        // 验证码校验
        if (registerDTO.getCaptchaId() != null && registerDTO.getCaptcha() != null) {
            boolean validate = captchaService.validate(registerDTO.getCaptchaId(), registerDTO.getCaptcha(), true);
            if (!validate) {
                throw new RenException("图形验证码错误，请重新获取");
            }
        }

        // 手机验证码校验
        if (registerDTO.getMobile() != null && registerDTO.getMobileCaptcha() != null) {
            boolean validate = captchaService.validateSMSValidateCode(registerDTO.getMobile(), registerDTO.getMobileCaptcha(), false);
            if (!validate) {
                throw new RenException("手机验证码错误，请重新获取");
            }
        }

        // 验证租户
        SysTenantEntity tenant = sysTenantService.getByTenantCode(registerDTO.getTenantCode());
        if (tenant == null) {
            throw new RenException("租户不存在");
        }
        if (tenant.getStatus() != 1) {
            throw new RenException("租户已被禁用");
        }

        // 检查用户名是否已存在
        SysUserDTO existingUser = sysUserService.getByUsername(registerDTO.getUsername());
        if (existingUser != null) {
            throw new RenException("用户名已存在");
        }

        // 创建用户
        SysUserDTO userDTO = new SysUserDTO();
        userDTO.setUsername(registerDTO.getUsername());
        userDTO.setPassword(registerDTO.getPassword());
        userDTO.setRealName(registerDTO.getRealName());
        userDTO.setMobile(registerDTO.getMobile());
        userDTO.setEmail(registerDTO.getEmail());
        userDTO.setTenantId(tenant.getId());
        userDTO.setUserType(2); // 租户用户
        userDTO.setStatus(1); // 启用状态

        sysUserService.save(userDTO);

        return new Result<>();
    }
}