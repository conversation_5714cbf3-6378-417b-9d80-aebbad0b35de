package xiaozhi.modules.security.controller;

import java.io.IOException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.TokenDTO;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.AssertUtils;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.security.dto.LoginDTO;
import xiaozhi.modules.security.dto.LoginRequestDTO;
import xiaozhi.modules.security.dto.LoginResponseDTO;
import xiaozhi.modules.security.dto.RegisterRequestDTO;
import xiaozhi.modules.security.dto.SmsVerificationDTO;
import xiaozhi.modules.security.dto.UserInfoResponseDTO;
import xiaozhi.modules.security.password.PasswordUtils;
import xiaozhi.modules.security.service.CaptchaService;
import xiaozhi.modules.security.service.SysUserTokenService;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.sys.dto.PasswordDTO;
import xiaozhi.modules.sys.dto.RetrievePasswordDTO;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.service.SysDictDataService;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.sys.service.SysUserService;
import xiaozhi.modules.sys.service.SysTenantService;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.security.tenant.TenantContext;
import xiaozhi.modules.sys.vo.SysDictDataItem;

/**
 * 身份认证控制层
 */
@AllArgsConstructor
@RestController
@RequestMapping("/auth")
@Tag(name = "身份认证", description = "用户登录、注册、信息获取等功能，支持多租户")
public class AuthController {
    private final SysUserService sysUserService;
    private final SysUserTokenService sysUserTokenService;
    private final CaptchaService captchaService;
    private final SysParamsService sysParamsService;
    private final SysDictDataService sysDictDataService;
    private final SysTenantService sysTenantService;

    @GetMapping("/captcha")
    @Operation(summary = "验证码")
    public void captcha(HttpServletResponse response, String uuid) throws IOException {
        // uuid不能为空
        AssertUtils.isBlank(uuid, ErrorCode.IDENTIFIER_NOT_NULL);
        // 生成验证码
        captchaService.create(response, uuid);
    }

    @PostMapping("/smsVerification")
    @Operation(summary = "短信验证码")
    public Result<Void> smsVerification(@RequestBody SmsVerificationDTO dto) {
        // 验证图形验证码
        boolean validate = captchaService.validate(dto.getCaptchaId(), dto.getCaptcha(), true);
        if (!validate) {
            throw new RenException("图形验证码错误");
        }
        Boolean isMobileRegister = sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class);
        if (!isMobileRegister) {
            throw new RenException("没有开启手机注册，没法使用短信验证码功能");
        }
        // 发送短信验证码
        captchaService.sendSMSValidateCode(dto.getPhone());
        return new Result<>();
    }

    @PostMapping("/login")
    @Operation(
        summary = "统一登录",
        description = "用户登录接口，自动识别用户租户并设置上下文"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "登录成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "401", description = "用户名或密码错误")
    })
    public Result<LoginResponseDTO> login(@RequestBody LoginRequestDTO loginRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(loginRequest);

        // 验证码校验
        if (loginRequest.getCaptchaId() != null && loginRequest.getCaptcha() != null) {
            boolean validate = captchaService.validate(loginRequest.getCaptchaId(), loginRequest.getCaptcha(), true);
            if (!validate) {
                throw new RenException("图形验证码错误，请重新获取");
            }
        }

        // 获取用户信息
        SysUserDTO userDTO = sysUserService.getByUsername(loginRequest.getUsername());
        if (userDTO == null) {
            throw new RenException("用户名或密码错误");
        }

        // 验证密码
        if (!PasswordUtils.matches(loginRequest.getPassword(), userDTO.getPassword())) {
            throw new RenException("用户名或密码错误");
        }

        // 检查用户状态
        if (userDTO.getStatus() != 1) {
            throw new RenException("用户已被禁用");
        }

        // 自动识别并设置租户上下文
        if (userDTO.getTenantId() != null) {
            SysTenantEntity tenant = sysTenantService.getById(userDTO.getTenantId());
            if (tenant != null && tenant.getStatus() == 1) {
                TenantContext.setTenantId(userDTO.getTenantId());
            } else if (tenant != null && tenant.getStatus() != 1) {
                throw new RenException("所属租户已被禁用");
            }
        }

        // 创建Token
        TokenDTO tokenDTO = sysUserTokenService.createToken(userDTO.getId());

        // 构建登录响应
        LoginResponseDTO response = new LoginResponseDTO();
        response.setToken(tokenDTO.getToken());
        response.setExpiresIn(tokenDTO.getExpire());
        response.setUserId(userDTO.getId());
        response.setUsername(userDTO.getUsername());
        response.setRealName(userDTO.getRealName());
        response.setUserType(userDTO.getUserType());
        response.setTenantId(userDTO.getTenantId());

        // 设置租户名称
        if (userDTO.getTenantId() != null) {
            SysTenantEntity tenant = sysTenantService.getById(userDTO.getTenantId());
            if (tenant != null) {
                response.setTenantName(tenant.getTenantName());
            }
        }

        // 设置管理员标识
        response.setIsPlatformAdmin(userDTO.getUserType() == 1);
        response.setIsTenantAdmin(userDTO.getUserType() == 2);

        return new Result<LoginResponseDTO>().ok(response);
    }

    @PostMapping("/register")
    @Operation(
        summary = "普通用户注册",
        description = "注册普通用户，默认无租户关联，用户类型为3"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "注册成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "409", description = "用户名已存在")
    })
    public Result<Void> register(@RequestBody RegisterRequestDTO registerRequest) {
        // 参数验证
        ValidatorUtils.validateEntity(registerRequest);

        // 检查是否允许注册
        if (!sysUserService.getAllowUserRegister()) {
            throw new RenException("当前不允许普通用户注册");
        }

        // 密码确认验证
        if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
            throw new RenException("两次输入的密码不一致");
        }

        // 验证码校验
        Boolean isMobileRegister = sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class);

        if (isMobileRegister && registerRequest.getMobile() != null) {
            // 手机注册模式，验证手机验证码
            if (registerRequest.getMobileCaptcha() == null) {
                throw new RenException("请输入手机验证码");
            }
            boolean validate = captchaService.validateSMSValidateCode(
                registerRequest.getMobile(), registerRequest.getMobileCaptcha(), false);
            if (!validate) {
                throw new RenException("手机验证码错误，请重新获取");
            }
        } else {
            // 图形验证码模式
            if (registerRequest.getCaptchaId() == null || registerRequest.getCaptcha() == null) {
                throw new RenException("请输入图形验证码");
            }
            boolean validate = captchaService.validate(
                registerRequest.getCaptchaId(), registerRequest.getCaptcha(), true);
            if (!validate) {
                throw new RenException("图形验证码错误，请重新获取");
            }
        }

        // 检查用户名是否已存在
        SysUserDTO existingUser = sysUserService.getByUsername(registerRequest.getUsername());
        if (existingUser != null) {
            throw new RenException("用户名已存在");
        }

        // 创建用户
        SysUserDTO userDTO = new SysUserDTO();
        userDTO.setUsername(registerRequest.getUsername());
        userDTO.setPassword(registerRequest.getPassword());
        userDTO.setRealName(registerRequest.getRealName());
        userDTO.setMobile(registerRequest.getMobile());
        userDTO.setEmail(registerRequest.getEmail());
        userDTO.setUserType(3); // 普通用户
        userDTO.setStatus(1); // 启用状态
        userDTO.setTenantId(null); // 默认无租户关联

        sysUserService.save(userDTO);

        return new Result<>();
    }

    @GetMapping("/info")
    @Operation(
        summary = "获取当前用户信息",
        description = "获取当前登录用户的详细信息，包括权限、角色等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    public Result<UserInfoResponseDTO> info() {
        Long userId = SecurityUser.getUserId();
        SysUserDTO userDTO = sysUserService.get(userId);

        if (userDTO == null) {
            throw new RenException("用户不存在");
        }

        // 构建用户信息响应
        UserInfoResponseDTO response = new UserInfoResponseDTO();
        response.setUserId(userDTO.getId());
        response.setUsername(userDTO.getUsername());
        response.setRealName(userDTO.getRealName());
        response.setHeadUrl(userDTO.getHeadUrl());
        response.setGender(userDTO.getGender());
        response.setEmail(userDTO.getEmail());
        response.setMobile(userDTO.getMobile());
        response.setUserType(userDTO.getUserType());
        response.setStatus(userDTO.getStatus());
        response.setTenantId(userDTO.getTenantId());
        response.setDeptId(userDTO.getDeptId());
        response.setCreateDate(userDTO.getCreateDate());

        // 设置租户名称
        if (userDTO.getTenantId() != null) {
            SysTenantEntity tenant = sysTenantService.getById(userDTO.getTenantId());
            if (tenant != null) {
                response.setTenantName(tenant.getTenantName());
            }
        }

        // 设置管理员标识
        response.setIsPlatformAdmin(userDTO.getUserType() == 1);
        response.setIsTenantAdmin(userDTO.getUserType() == 2);
        response.setIsSuperAdmin(userDTO.getSuperAdmin() == 1);

        // TODO: 设置角色和权限信息
        // response.setRoles(roleService.getUserRoles(userId));
        // response.setPermissions(permissionService.getUserPermissions(userId));

        return new Result<UserInfoResponseDTO>().ok(response);
    }

    @PutMapping("/change-password")
    @Operation(summary = "修改用户密码")
    public Result<?> changePassword(@RequestBody PasswordDTO passwordDTO) {
        // 判断非空
        ValidatorUtils.validateEntity(passwordDTO);
        Long userId = SecurityUser.getUserId();
        sysUserTokenService.changePassword(userId, passwordDTO);
        return new Result<>();
    }

    @PutMapping("/retrieve-password")
    @Operation(summary = "找回密码")
    public Result<?> retrievePassword(@RequestBody RetrievePasswordDTO dto) {
        // 是否开启手机注册
        Boolean isMobileRegister = sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class);
        if (!isMobileRegister) {
            throw new RenException("没有开启手机注册，没法使用找回密码功能");
        }
        // 判断非空
        ValidatorUtils.validateEntity(dto);
        // 验证用户是否是手机号码
        boolean validPhone = ValidatorUtils.isValidPhone(dto.getPhone());
        if (!validPhone) {
            throw new RenException("输入的手机号码格式不正确");
        }

        // 按照用户名获取用户
        SysUserDTO userDTO = sysUserService.getByUsername(dto.getPhone());
        if (userDTO == null) {
            throw new RenException("输入的手机号码未注册");
        }
        // 验证短信验证码是否正常
        boolean validate = captchaService.validateSMSValidateCode(dto.getPhone(), dto.getCode(), false);
        // 判断是否通过验证
        if (!validate) {
            throw new RenException("输入的手机验证码错误");
        }

        sysUserService.changePasswordDirectly(userDTO.getId(), dto.getPassword());
        return new Result<>();
    }

    @GetMapping("/pub-config")
    @Operation(summary = "公共配置")
    public Result<Map<String, Object>> pubConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enableMobileRegister", sysParamsService
                .getValueObject(Constant.SysMSMParam.SERVER_ENABLE_MOBILE_REGISTER.getValue(), Boolean.class));
        config.put("version", Constant.VERSION);
        config.put("year", "©" + Calendar.getInstance().get(Calendar.YEAR));
        config.put("allowUserRegister", sysUserService.getAllowUserRegister());
        List<SysDictDataItem> list = sysDictDataService.getDictDataByType(Constant.DictType.MOBILE_AREA.getValue());
        config.put("mobileAreaList", list);
        config.put("beianIcpNum", sysParamsService.getValue(Constant.SysBaseParam.BEIAN_ICP_NUM.getValue(), true));
        config.put("beianGaNum", sysParamsService.getValue(Constant.SysBaseParam.BEIAN_GA_NUM.getValue(), true));
        config.put("name", sysParamsService.getValue(Constant.SysBaseParam.SERVER_NAME.getValue(), true));

        return new Result<Map<String, Object>>().ok(config);
    }


}