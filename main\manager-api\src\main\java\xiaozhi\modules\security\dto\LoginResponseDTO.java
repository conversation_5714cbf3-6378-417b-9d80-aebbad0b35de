package xiaozhi.modules.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "登录响应")
public class LoginResponseDTO {
    
    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;
    
    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";
    
    @Schema(description = "过期时间（秒）", example = "7200")
    private Long expiresIn;
    
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    @Schema(description = "真实姓名", example = "管理员")
    private String realName;
    
    @Schema(description = "用户类型", example = "1")
    private Integer userType;
    
    @Schema(description = "租户ID", example = "1")
    private Long tenantId;
    
    @Schema(description = "租户名称", example = "测试租户")
    private String tenantName;
    
    @Schema(description = "是否平台管理员", example = "true")
    private Boolean isPlatformAdmin;
    
    @Schema(description = "是否租户管理员", example = "false")
    private Boolean isTenantAdmin;
}
