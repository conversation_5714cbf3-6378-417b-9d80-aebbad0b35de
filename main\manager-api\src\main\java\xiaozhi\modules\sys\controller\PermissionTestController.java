package xiaozhi.modules.sys.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;

/**
 * 权限测试Controller
 * 用于演示权限框架的使用
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/test/permission")
@Tag(name = "权限测试")
public class PermissionTestController {
    
    @GetMapping("/platform/admin")
    @Operation(summary = "平台管理员权限测试")
    @RequiresPermission(value = "organization:view:*", description = "需要平台管理员权限")
    public Result<String> platformAdminTest() {
        return new Result<String>().ok("平台管理员权限验证通过");
    }
    
    @GetMapping("/tenant/admin")
    @Operation(summary = "租户管理员权限测试")
    @RequiresPermission(value = "user:create:tenant", tenant = true, description = "需要租户管理员权限")
    public Result<String> tenantAdminTest() {
        return new Result<String>().ok("租户管理员权限验证通过");
    }
    
    @GetMapping("/device/manager")
    @Operation(summary = "设备管理员权限测试")
    @RequiresPermission(value = "device:bind:tenant", tenant = true, description = "需要设备管理权限")
    public Result<String> deviceManagerTest() {
        return new Result<String>().ok("设备管理权限验证通过");
    }
    
    @GetMapping("/tenant/user")
    @Operation(summary = "租户普通用户权限测试")
    @RequiresPermission(value = "device:view:tenant", tenant = true, description = "需要设备查看权限")
    public Result<String> tenantUserTest() {
        return new Result<String>().ok("租户用户权限验证通过");
    }
    
    @GetMapping("/system/config")
    @Operation(summary = "系统配置权限测试")
    @RequiresPermission(value = "system:config:*", description = "需要系统配置权限")
    public Result<String> systemConfigTest() {
        return new Result<String>().ok("系统配置权限验证通过");
    }
    
    @GetMapping("/public")
    @Operation(summary = "公开接口测试")
    public Result<String> publicTest() {
        return new Result<String>().ok("公开接口，无需权限验证");
    }
}
