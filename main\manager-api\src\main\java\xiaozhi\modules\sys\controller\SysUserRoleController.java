package xiaozhi.modules.sys.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.entity.SysRoleEntity;
import xiaozhi.modules.sys.entity.SysUserRoleEntity;
import xiaozhi.modules.sys.service.SysRoleService;
import xiaozhi.modules.sys.service.SysUserRoleService;
import xiaozhi.modules.sys.service.SysUserService;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色分配Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/user-role")
@Tag(name = "用户角色分配", description = "用户角色分配管理，包括角色分配、取消分配、查询用户角色等功能")
@AllArgsConstructor
public class SysUserRoleController {
    
    private final SysUserRoleService sysUserRoleService;
    private final SysUserService sysUserService;
    private final SysRoleService sysRoleService;
    
    @GetMapping("/user/{userId}/roles")
    @Operation(
        summary = "获取用户角色列表",
        description = "获取指定用户拥有的所有角色列表，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "查看用户角色权限")
    public Result<List<SysRoleEntity>> getUserRoles(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<Long> roleIds = sysUserRoleService.getRoleIdsByUserId(userId);
        List<SysRoleEntity> roles = roleIds.stream()
            .map(roleId -> sysRoleService.getById(roleId))
            .filter(role -> role != null)
            .collect(Collectors.toList());

        return new Result<List<SysRoleEntity>>().ok(roles);
    }
    
    @GetMapping("/role/{roleId}/users")
    @Operation(
        summary = "获取角色用户列表",
        description = "获取拥有指定角色的所有用户列表，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "角色不存在")
    })
    @RequiresPermission(value = "role:view:tenant", tenant = true, description = "查看角色用户权限")
    public Result<List<SysUserDTO>> getRoleUsers(
            @Parameter(description = "角色ID", required = true, example = "1")
            @PathVariable Long roleId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<Long> userIds = sysUserRoleService.getUserIdsByRoleId(roleId);
        List<SysUserDTO> users = userIds.stream()
            .map(userId -> sysUserService.get(userId))
            .filter(user -> user != null)
            .collect(Collectors.toList());

        return new Result<List<SysUserDTO>>().ok(users);
    }
    
    @PostMapping("/assign")
    @Operation(
        summary = "分配用户角色",
        description = "为用户分配一个或多个角色，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "分配成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "user:update:tenant", tenant = true, description = "分配用户角色权限")
    public Result<Void> assignRoles(@RequestBody UserRoleAssignDTO assignDTO) {
        // 验证角色是否存在（租户过滤由拦截器处理）
        for (Long roleId : assignDTO.getRoleIds()) {
            SysRoleEntity role = sysRoleService.getById(roleId);
            if (role == null) {
                throw new RuntimeException("角色不存在: " + roleId);
            }
        }

        // 先删除用户现有角色
        sysUserRoleService.deleteByUserId(assignDTO.getUserId());

        // 分配新角色
        List<SysUserRoleEntity> userRoles = assignDTO.getRoleIds().stream()
            .map(roleId -> {
                SysUserRoleEntity userRole = new SysUserRoleEntity();
                userRole.setUserId(assignDTO.getUserId());
                userRole.setRoleId(roleId);
                userRole.setCreator(SecurityUser.getUserId());
                userRole.setCreateDate(new Date());
                return userRole;
            })
            .collect(Collectors.toList());

        if (!userRoles.isEmpty()) {
            sysUserRoleService.batchInsert(userRoles);
        }

        return new Result<>();
    }
    
    @DeleteMapping("/{userId}/{roleId}")
    @Operation(
        summary = "取消用户角色",
        description = "取消用户的指定角色，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "取消成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户或角色不存在")
    })
    @RequiresPermission(value = "user:update:tenant", tenant = true, description = "取消用户角色权限")
    public Result<Void> removeUserRole(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId,
            @Parameter(description = "角色ID", required = true, example = "1")
            @PathVariable Long roleId) {

        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        sysUserRoleService.deleteByUserIdAndRoleId(userId, roleId);

        return new Result<>();
    }

    @GetMapping("/available-roles")
    @Operation(
        summary = "获取可分配角色列表",
        description = "获取当前租户可以分配给用户的角色列表，MyBatis拦截器自动处理租户过滤"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "role:view:tenant", tenant = true, description = "查看可分配角色权限")
    public Result<List<SysRoleEntity>> getAvailableRoles() {
        // 直接调用Service，租户过滤由MyBatis拦截器自动处理
        List<SysRoleEntity> roles = sysRoleService.list();
        return new Result<List<SysRoleEntity>>().ok(roles);
    }
    
    /**
     * 用户角色分配DTO
     */
    public static class UserRoleAssignDTO {
        private Long userId;
        private List<Long> roleIds;
        
        public Long getUserId() {
            return userId;
        }
        
        public void setUserId(Long userId) {
            this.userId = userId;
        }
        
        public List<Long> getRoleIds() {
            return roleIds;
        }
        
        public void setRoleIds(List<Long> roleIds) {
            this.roleIds = roleIds;
        }
    }
}
