package xiaozhi.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.security.password.PasswordUtils;
import xiaozhi.modules.sys.dao.SysUserDao;
import xiaozhi.modules.sys.dto.*;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.modules.sys.enums.SuperAdminEnum;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.sys.service.SysUserService;
import xiaozhi.modules.sys.vo.AdminPageUserVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 系统用户
 */
@AllArgsConstructor
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
    private final SysUserDao sysUserDao;

    private final DeviceService deviceService;

    private final AgentService agentService;

    private final SysParamsService sysParamsService;

    @Override
    public SysUserDTO getByUsername(String username) {
        QueryWrapper<SysUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        List<SysUserEntity> users = sysUserDao.selectList(queryWrapper);
        if (users == null || users.isEmpty()) {
            return null;
        }
        SysUserEntity entity = users.getFirst();
        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    public SysUserDTO getByUserId(Long userId) {
        SysUserEntity sysUserEntity = sysUserDao.selectById(userId);

        return ConvertUtils.sourceToTarget(sysUserEntity, SysUserDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysUserDTO dto) {
        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

        // 密码强度
        if (!isStrongPassword(entity.getPassword())) {
            throw new RenException(ErrorCode.PASSWORD_WEAK_ERROR);
        }

        // 密码加密
        String password = PasswordUtils.encode(entity.getPassword());
        entity.setPassword(password);

        // 保存用户
        Long userCount = getUserCount();
        if (userCount == 0) {
            entity.setSuperAdmin(SuperAdminEnum.YES.value());
        } else {
            entity.setSuperAdmin(SuperAdminEnum.NO.value());
        }
        entity.setStatus(1);

        insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        // 删除用户
        baseDao.deleteById(id);
        // 删除设备
        deviceService.deleteByUserId(id);
        // 删除智能体
        agentService.deleteAgentByUserId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(Long userId, PasswordDTO passwordDTO) {
        SysUserEntity sysUserEntity = sysUserDao.selectById(userId);

        if (null == sysUserEntity) {
            throw new RenException(ErrorCode.TOKEN_INVALID);
        }

        // 判断旧密码是否正确
        if (!PasswordUtils.matches(passwordDTO.getPassword(), sysUserEntity.getPassword())) {
            throw new RenException("旧密码输入错误");
        }

        // 新密码强度
        if (!isStrongPassword(passwordDTO.getNewPassword())) {
            throw new RenException(ErrorCode.PASSWORD_WEAK_ERROR);
        }

        // 密码加密
        String password = PasswordUtils.encode(passwordDTO.getNewPassword());
        sysUserEntity.setPassword(password);

        updateById(sysUserEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePasswordDirectly(Long userId, String password) {
        // 新密码强度
        if (!isStrongPassword(password)) {
            throw new RenException(ErrorCode.PASSWORD_WEAK_ERROR);
        }
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setId(userId);
        sysUserEntity.setPassword(PasswordUtils.encode(password));
        updateById(sysUserEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String resetPassword(Long userId) {
        String password = generatePassword();
        changePasswordDirectly(userId, password);
        return password;
    }

    private Long getUserCount() {
        QueryWrapper<SysUserEntity> queryWrapper = new QueryWrapper<>();
        return baseDao.selectCount(queryWrapper);
    }

    @Override
    public PageData<AdminPageUserVO> page(AdminPageUserDTO dto) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put(Constant.PAGE, dto.getPage());
        params.put(Constant.LIMIT, dto.getLimit());
        IPage<SysUserEntity> page = baseDao.selectPage(
                getPage(params, "id", true),
                new QueryWrapper<SysUserEntity>().like(StringUtils.isNotBlank(dto.getMobile()), "username",
                        dto.getMobile()));
        // 循环处理page获取回来的数据，返回需要的字段
        List<AdminPageUserVO> list = page.getRecords().stream().map(user -> {
            AdminPageUserVO adminPageUserVO = new AdminPageUserVO();
            adminPageUserVO.setUserid(user.getId().toString());
            adminPageUserVO.setMobile(user.getUsername());
            String deviceCount = deviceService.selectCountByUserId(user.getId()).toString();
            adminPageUserVO.setDeviceCount(deviceCount);
            adminPageUserVO.setStatus(user.getStatus());
            adminPageUserVO.setCreateDate(user.getCreateDate());
            return adminPageUserVO;
        }).toList();
        return new PageData<>(list, page.getTotal());
    }

    private boolean isStrongPassword(String password) {
        // 弱密码的正则表达式
        String weakPasswordRegex = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).+$";
        Pattern pattern = Pattern.compile(weakPasswordRegex);
        Matcher matcher = pattern.matcher(password);
        return matcher.matches();
    }

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
    private static final Random random = new Random();

    /**
     * 生成随机密码
     * 
     * @return 随机生成的密码
     */
    private String generatePassword() {
        StringBuilder password = new StringBuilder();
        for (int i = 0; i < 12; i++) {
            password.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return password.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(Integer status, String[] userIds) {
        for (String userId : userIds) {
            SysUserEntity entity = new SysUserEntity();
            entity.setId(Long.parseLong(userId));
            entity.setStatus(status);
            updateById(entity);
        }
    }

    @Override
    public boolean getAllowUserRegister() {
        String allowUserRegister = sysParamsService.getValue(Constant.SERVER_ALLOW_USER_REGISTER, true);
        if (allowUserRegister.equals("true")) {
            return true;
        }
        Long userCount = baseDao.selectCount(new QueryWrapper<SysUserEntity>());
        if (userCount == 0) {
            return true;
        }
        return false;
    }

    @Override
    public PageData<SysUserDTO> page(Map<String, Object> params) {
        IPage<SysUserEntity> page = baseDao.selectPage(
            getPage(params, "create_date", false),
            getWrapper(params)
        );

        return getPageData(page, SysUserDTO.class);
    }

    @Override
    public SysUserDTO get(Long id) {
        SysUserEntity entity = baseDao.selectById(id);
        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    public void update(SysUserDTO dto) {
        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);
        updateById(entity);
    }

    @Override
    public void deleteBatch(Long[] ids) {
        for (Long id : ids) {
            deleteById(id);
        }
    }

    @Override
    public List<SysUserDTO> convertToDTO(List<SysUserEntity> entityList) {
        return ConvertUtils.sourceToTarget(entityList, SysUserDTO.class);
    }

    @Override
    public PageData<UserDetailResponseDTO> pageWithDetails(UserQueryRequestDTO queryRequest) {
        Map<String, Object> params = Map.of(
            "page", queryRequest.getPage(),
            "limit", queryRequest.getLimit()
        );

        QueryWrapper<SysUserEntity> wrapper = new QueryWrapper<>();

        // 添加查询条件
        if (queryRequest.getUsername() != null && !queryRequest.getUsername().isEmpty()) {
            wrapper.like("username", queryRequest.getUsername());
        }
        if (queryRequest.getRealName() != null && !queryRequest.getRealName().isEmpty()) {
            wrapper.like("real_name", queryRequest.getRealName());
        }
        if (queryRequest.getMobile() != null && !queryRequest.getMobile().isEmpty()) {
            wrapper.like("mobile", queryRequest.getMobile());
        }
        if (queryRequest.getEmail() != null && !queryRequest.getEmail().isEmpty()) {
            wrapper.like("email", queryRequest.getEmail());
        }
        if (queryRequest.getUserType() != null) {
            wrapper.eq("user_type", queryRequest.getUserType());
        }
        if (queryRequest.getStatus() != null) {
            wrapper.eq("status", queryRequest.getStatus());
        }
        if (queryRequest.getDeptId() != null) {
            wrapper.eq("dept_id", queryRequest.getDeptId());
        }

        wrapper.eq("deleted", 0);
        wrapper.orderByDesc("create_date");

        IPage<SysUserEntity> page = baseDao.selectPage(
            getPage(params, "create_date", false),
            wrapper
        );

        List<UserDetailResponseDTO> list = page.getRecords().stream()
            .map(this::convertToUserDetail)
            .toList();

        return new PageData<>(list, page.getTotal());
    }

    @Override
    public UserDetailResponseDTO getUserDetail(Long userId) {
        SysUserEntity entity = baseDao.selectById(userId);
        if (entity == null) {
            return null;
        }
        return convertToUserDetail(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUser(UserCreateRequestDTO createRequest) {
        // 检查用户名是否已存在
        if (getByUsername(createRequest.getUsername()) != null) {
            throw new RenException("用户名已存在");
        }

        SysUserEntity entity = new SysUserEntity();
        entity.setUsername(createRequest.getUsername());
        entity.setPassword(PasswordUtils.encode(createRequest.getPassword()));
        entity.setRealName(createRequest.getRealName());
        entity.setHeadUrl(createRequest.getHeadUrl());
        entity.setGender(createRequest.getGender());
        entity.setEmail(createRequest.getEmail());
        entity.setMobile(createRequest.getMobile());
        entity.setUserType(createRequest.getUserType());
        entity.setStatus(createRequest.getStatus());
        entity.setDeptId(createRequest.getDeptId());

        // 自动设置租户ID（非平台管理员）
        if (createRequest.getUserType() != 1 && createRequest.getTenantId() != null) {
            entity.setTenantId(createRequest.getTenantId());
        }

        // 设置超级管理员标识
        Long userCount = getUserCount();
        if (userCount == 0) {
            entity.setSuperAdmin(SuperAdminEnum.YES.value());
        } else {
            entity.setSuperAdmin(SuperAdminEnum.NO.value());
        }

        insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserUpdateRequestDTO updateRequest) {
        SysUserEntity entity = baseDao.selectById(updateRequest.getId());
        if (entity == null) {
            throw new RenException("用户不存在");
        }

        // 检查用户名是否被其他用户使用
        SysUserDTO existingUser = getByUsername(updateRequest.getUsername());
        if (existingUser != null && !existingUser.getId().equals(updateRequest.getId())) {
            throw new RenException("用户名已被其他用户使用");
        }

        entity.setUsername(updateRequest.getUsername());
        entity.setRealName(updateRequest.getRealName());
        entity.setHeadUrl(updateRequest.getHeadUrl());
        entity.setGender(updateRequest.getGender());
        entity.setEmail(updateRequest.getEmail());
        entity.setMobile(updateRequest.getMobile());
        entity.setUserType(updateRequest.getUserType());
        entity.setStatus(updateRequest.getStatus());
        entity.setDeptId(updateRequest.getDeptId());

        // 自动设置租户ID（非平台管理员）
        if (updateRequest.getUserType() != 1 && updateRequest.getTenantId() != null) {
            entity.setTenantId(updateRequest.getTenantId());
        }

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long userId) {
        // 删除用户及相关数据
        deleteById(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, Integer status) {
        SysUserEntity entity = new SysUserEntity();
        entity.setId(userId);
        entity.setStatus(status);
        updateById(entity);
    }

    @Override
    public List<UserDetailResponseDTO> getAllUsers() {
        QueryWrapper<SysUserEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("deleted", 0);
        wrapper.orderByDesc("create_date");

        List<SysUserEntity> entities = baseDao.selectList(wrapper);
        return entities.stream()
            .map(this::convertToUserDetail)
            .toList();
    }

    /**
     * 转换为用户详情DTO
     */
    private UserDetailResponseDTO convertToUserDetail(SysUserEntity entity) {
        UserDetailResponseDTO dto = new UserDetailResponseDTO();
        dto.setId(entity.getId());
        dto.setUsername(entity.getUsername());
        dto.setRealName(entity.getRealName());
        dto.setHeadUrl(entity.getHeadUrl());
        dto.setGender(entity.getGender());
        dto.setEmail(entity.getEmail());
        dto.setMobile(entity.getMobile());
        dto.setUserType(entity.getUserType());
        dto.setStatus(entity.getStatus());
        dto.setTenantId(entity.getTenantId());
        dto.setDeptId(entity.getDeptId());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());

        // 设置用户类型名称
        switch (entity.getUserType()) {
            case 1:
                dto.setUserTypeName("平台管理员");
                break;
            case 2:
                dto.setUserTypeName("租户用户");
                break;
            case 3:
                dto.setUserTypeName("普通用户");
                break;
            default:
                dto.setUserTypeName("未知");
        }

        // 设置状态名称
        dto.setStatusName(entity.getStatus() == 1 ? "正常" : "禁用");

        // 设置管理员标识
        dto.setIsPlatformAdmin(entity.getUserType() == 1);
        dto.setIsTenantAdmin(entity.getUserType() == 2);
        dto.setIsSuperAdmin(entity.getSuperAdmin() == 1);

        return dto;
    }
}
