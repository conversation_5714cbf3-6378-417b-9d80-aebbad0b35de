package xiaozhi.modules.sys.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.sys.entity.SysPermissionEntity;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.sys.service.SysPermissionService;
import xiaozhi.modules.sys.service.SysTenantService;

/**
 * Service方法演示Controller
 * 演示BaseService中新增方法的使用
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/demo/service")
@Tag(name = "Service方法演示")
@AllArgsConstructor
public class ServiceMethodDemoController {
    
    private final SysPermissionService sysPermissionService;
    private final SysTenantService sysTenantService;
    
    @GetMapping("/permission/count")
    @Operation(summary = "演示权限统计方法")
    public Result<Object> permissionCountDemo() {
        // 演示 count() 方法
        long totalCount = sysPermissionService.count();
        
        // 演示 count(Wrapper) 方法
        QueryWrapper<SysPermissionEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);
        long activeCount = sysPermissionService.count(wrapper);
        
        return new Result<>().ok(Map.of(
            "totalCount", totalCount,
            "activeCount", activeCount,
            "message", "演示 SysPermissionService.count() 方法"
        ));
    }
    
    @PostMapping("/permission/save")
    @Operation(summary = "演示权限保存方法")
    public Result<String> permissionSaveDemo() {
        // 演示 save() 方法
        SysPermissionEntity permission = new SysPermissionEntity();
        permission.setPermission("demo:test:" + System.currentTimeMillis());
        permission.setDescription("演示权限");
        permission.setDomain("demo");
        permission.setAction("test");
        permission.setInstance("*");
        permission.setStatus(1);
        permission.setCreator(1L);
        permission.setCreateDate(new Date());
        
        boolean saved = sysPermissionService.save(permission);
        
        return new Result<String>().ok(saved ? "保存成功，演示 SysPermissionService.save() 方法" : "保存失败");
    }
    
    @GetMapping("/tenant/list")
    @Operation(summary = "演示租户列表方法")
    public Result<Object> tenantListDemo() {
        // 演示 list() 方法
        List<SysTenantEntity> allTenants = sysTenantService.list();
        
        // 演示 list(Wrapper) 方法
        QueryWrapper<SysTenantEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);
        List<SysTenantEntity> activeTenants = sysTenantService.list(wrapper);
        
        return new Result<>().ok(Map.of(
            "allTenants", allTenants,
            "activeTenants", activeTenants,
            "message", "演示 SysTenantService.list() 方法"
        ));
    }
    
    @GetMapping("/tenant/{id}")
    @Operation(summary = "演示租户查询方法")
    public Result<Object> tenantGetByIdDemo(@PathVariable Long id) {
        // 演示 getById() 方法
        SysTenantEntity tenant = sysTenantService.getById(id);
        
        return new Result<>().ok(Map.of(
            "tenant", tenant,
            "message", "演示 SysTenantService.getById() 方法"
        ));
    }
    
    @GetMapping("/methods/summary")
    @Operation(summary = "方法总结")
    public Result<Object> methodsSummary() {
        return new Result<>().ok(Map.of(
            "baseServiceMethods", List.of(
                "save(T entity) - 保存实体",
                "getById(Serializable id) - 根据ID查询",
                "list() - 查询所有记录",
                "list(Wrapper<T> queryWrapper) - 条件查询",
                "count() - 统计所有记录",
                "count(Wrapper<T> queryWrapper) - 条件统计"
            ),
            "inheritedServices", List.of(
                "SysPermissionService",
                "SysTenantService", 
                "SysRoleService",
                "SysOrganizationService"
            ),
            "message", "所有Service都继承自BaseService，拥有完整的CRUD方法"
        ));
    }
}
