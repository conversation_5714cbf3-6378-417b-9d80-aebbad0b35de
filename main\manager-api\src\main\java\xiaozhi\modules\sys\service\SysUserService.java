package xiaozhi.modules.sys.service;

import java.util.List;
import java.util.Map;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.dto.*;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.modules.sys.vo.AdminPageUserVO;

/**
 * 系统用户
 */
public interface SysUserService extends BaseService<SysUserEntity> {

    SysUserDTO getByUsername(String username);

    SysUserDTO getByUserId(Long userId);

    void save(SysUserDTO dto);

    /**
     * 删除指定用户，且有关联的数据设备和智能体
     * 
     * @param ids
     */
    void deleteById(Long ids);

    /**
     * 验证是否允许修改密码更改
     * 
     * @param userId      用户id
     * @param passwordDTO 验证密码的参数
     */
    void changePassword(Long userId, PasswordDTO passwordDTO);

    /**
     * 直接修改密码，不需要验证
     * 
     * @param userId   用户id
     * @param password 密码
     */
    void changePasswordDirectly(Long userId, String password);

    /**
     * 重置密码
     * 
     * @param userId 用户id
     * @return 随机生成符合规范的密码
     */
    String resetPassword(Long userId);

    /**
     * 管理员分页用户信息
     * 
     * @param dto 分页查找参数
     * @return 用户列表分页数据
     */
    PageData<AdminPageUserVO> page(AdminPageUserDTO dto);

    /**
     * 批量修改用户状态
     * 
     * @param status  用户状态
     * @param userIds 用户ID数组
     */
    void changeStatus(Integer status, String[] userIds);

    /**
     * 获取是否允许用户注册
     *
     * @return 是否允许用户注册
     */
    boolean getAllowUserRegister();

    /**
     * 分页查询用户（支持多条件）
     *
     * @param params 查询参数
     * @return 分页数据
     */
    PageData<SysUserDTO> page(Map<String, Object> params);

    /**
     * 根据ID获取用户信息
     *
     * @param id 用户ID
     * @return 用户DTO
     */
    SysUserDTO get(Long id);

    /**
     * 更新用户信息
     *
     * @param dto 用户DTO
     */
    void update(SysUserDTO dto);

    /**
     * 批量删除用户
     *
     * @param ids 用户ID数组
     */
    void deleteBatch(Long[] ids);

    /**
     * 将实体列表转换为DTO列表
     *
     * @param entityList 实体列表
     * @return DTO列表
     */
    List<SysUserDTO> convertToDTO(List<SysUserEntity> entityList);

    /**
     * 分页查询用户详情（支持多条件）
     *
     * @param queryRequest 查询请求
     * @return 分页数据
     */
    PageData<UserDetailResponseDTO> pageWithDetails(UserQueryRequestDTO queryRequest);

    /**
     * 根据ID获取用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    UserDetailResponseDTO getUserDetail(Long userId);

    /**
     * 创建用户
     *
     * @param createRequest 创建请求
     */
    void createUser(UserCreateRequestDTO createRequest);

    /**
     * 更新用户
     *
     * @param updateRequest 更新请求
     */
    void updateUser(UserUpdateRequestDTO updateRequest);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     */
    void deleteUser(Long userId);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 状态
     */
    void updateUserStatus(Long userId, Integer status);

    /**
     * 获取所有用户详情列表
     *
     * @return 用户详情列表
     */
    List<UserDetailResponseDTO> getAllUsers();
}
