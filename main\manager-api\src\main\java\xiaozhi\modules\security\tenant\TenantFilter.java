package xiaozhi.modules.security.tenant;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import xiaozhi.common.user.UserDetail;
import xiaozhi.modules.security.user.SecurityUser;

/**
 * 租户过滤器
 * 在请求开始时设置租户上下文
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Component
public class TenantFilter implements Filter {
    
    private static final Logger logger = LoggerFactory.getLogger(TenantFilter.class);
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        try {
            // 设置租户上下文
            setTenantContext(httpRequest);
            
            // 继续执行请求
            chain.doFilter(request, response);
        } finally {
            // 清除租户上下文
            TenantContext.clear();
        }
    }
    
    /**
     * 设置租户上下文
     */
    private void setTenantContext(HttpServletRequest request) {
        try {
            // 优先从请求头获取租户ID
            String tenantIdHeader = request.getHeader("X-Tenant-Id");
            if (tenantIdHeader != null && !tenantIdHeader.isEmpty()) {
                Long tenantId = Long.parseLong(tenantIdHeader);
                TenantContext.setTenantId(tenantId);
                logger.debug("从请求头设置租户ID: {}", tenantId);
                return;
            }
            
            // 从当前用户获取租户ID
            UserDetail user = SecurityUser.getUser();
            if (user != null && user.getTenantId() != null) {
                TenantContext.setTenantId(user.getTenantId());
                logger.debug("从用户信息设置租户ID: {}", user.getTenantId());
                return;
            }
            
            logger.debug("未设置租户ID");
        } catch (Exception e) {
            logger.warn("设置租户上下文失败: {}", e.getMessage());
        }
    }
}
