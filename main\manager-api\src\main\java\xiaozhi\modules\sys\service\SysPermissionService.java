package xiaozhi.modules.sys.service;

import java.util.List;
import java.util.Set;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.entity.SysPermissionEntity;

/**
 * 权限Service
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface SysPermissionService extends BaseService<SysPermissionEntity> {
    
    /**
     * 根据用户ID获取权限列表
     * 
     * @param userId 用户ID
     * @return 权限字符串集合
     */
    Set<String> getUserPermissions(Long userId);
    
    /**
     * 根据角色ID获取权限列表
     * 
     * @param roleId 角色ID
     * @return 权限实体列表
     */
    List<SysPermissionEntity> getPermissionsByRoleId(Long roleId);
    
    /**
     * 根据权限域和操作获取权限列表
     * 
     * @param domain 权限域
     * @param action 操作类型
     * @return 权限实体列表
     */
    List<SysPermissionEntity> getPermissionsByDomainAndAction(String domain, String action);
    
    /**
     * 检查用户是否有指定权限
     * 
     * @param userId 用户ID
     * @param permission 权限字符串
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permission);
    
    /**
     * 检查用户是否有租户权限
     * 
     * @param userId 用户ID
     * @param permission 权限字符串
     * @param tenantId 租户ID
     * @return 是否有权限
     */
    boolean hasTenantPermission(Long userId, String permission, Long tenantId);
}
