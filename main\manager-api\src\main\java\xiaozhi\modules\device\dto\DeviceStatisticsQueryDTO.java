package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备统计查询DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "设备统计查询")
public class DeviceStatisticsQueryDTO {
    
    @Schema(description = "设备ID", example = "device123")
    private String deviceId;
    
    @Schema(description = "设备MAC地址", example = "AA:BB:CC:DD:EE:FF")
    private String macAddress;
    
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    
    @Schema(description = "智能体ID", example = "agent123")
    private String agentId;
    
    @Schema(description = "开始日期", required = true, example = "2025-01-01")
    private String startDate;
    
    @Schema(description = "结束日期", required = true, example = "2025-01-31")
    private String endDate;
    
    @Schema(description = "统计类型", example = "daily", allowableValues = {"daily", "weekly", "monthly"})
    private String statisticsType = "daily";
}
