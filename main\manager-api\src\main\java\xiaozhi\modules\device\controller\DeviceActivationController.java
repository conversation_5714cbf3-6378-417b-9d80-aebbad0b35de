package xiaozhi.modules.device.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.device.dto.DeviceActivationQueryDTO;
import xiaozhi.modules.device.entity.DeviceActivationEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.security.annotation.RequiresPermission;

import javax.validation.Valid;

/**
 * 设备激活管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@RestController
@RequestMapping("/device/activation")
@Tag(name = "设备激活管理", description = "设备激活流程和记录管理")
public class DeviceActivationController {
    
    private final DeviceService deviceService;
    
    @PostMapping("/page")
    @Operation(
        operationId = "pageDeviceActivations",
        summary = "分页查询设备激活记录",
        description = "根据查询条件分页获取设备激活记录"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "device:activation:view", description = "查看设备激活记录")
    public Result<PageData<DeviceActivationEntity>> pageActivations(
            @Parameter(description = "查询条件", required = true)
            @RequestBody @Valid DeviceActivationQueryDTO queryDTO) {
        PageData<DeviceActivationEntity> page = deviceService.pageActivations(queryDTO);
        return new Result<PageData<DeviceActivationEntity>>().ok(page);
    }
    
    @PostMapping("/create")
    @Operation(
        operationId = "createActivationRecord",
        summary = "创建设备激活记录",
        description = "为设备创建激活记录和激活码"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "device:activation:create", description = "创建设备激活记录")
    public Result<DeviceActivationEntity> createActivationRecord(
            @Parameter(description = "设备MAC地址", required = true, example = "AA:BB:CC:DD:EE:FF")
            @RequestParam String macAddress,
            @Parameter(description = "智能体ID", required = true, example = "agent123")
            @RequestParam String agentId,
            @Parameter(description = "激活码", required = true, example = "123456")
            @RequestParam String activationCode) {
        DeviceActivationEntity activation = deviceService.createActivationRecord(macAddress, agentId, activationCode);
        return new Result<DeviceActivationEntity>().ok(activation);
    }
    
    @PostMapping("/update-status")
    @Operation(
        operationId = "updateActivationStatus",
        summary = "更新设备激活状态",
        description = "更新设备的激活状态"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "device:activation:update", description = "更新设备激活状态")
    public Result<Void> updateActivationStatus(
            @Parameter(description = "激活码", required = true, example = "123456")
            @RequestParam String activationCode,
            @Parameter(description = "激活状态", required = true, example = "1")
            @RequestParam Integer status,
            @Parameter(description = "设备ID", example = "device123")
            @RequestParam(required = false) String deviceId,
            @Parameter(description = "失败原因", example = "激活码过期")
            @RequestParam(required = false) String failureReason) {
        deviceService.updateActivationStatus(activationCode, status, deviceId, failureReason);
        return new Result<Void>().ok();
    }
    
    @GetMapping("/code/{activationCode}")
    @Operation(
        operationId = "getActivationByCode",
        summary = "根据激活码获取激活记录",
        description = "根据激活码查询激活记录详情"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "激活记录不存在")
    })
    @RequiresPermission(value = "device:activation:view", description = "查看激活记录详情")
    public Result<DeviceActivationEntity> getActivationByCode(
            @Parameter(description = "激活码", required = true, example = "123456")
            @PathVariable String activationCode) {
        // 这里需要通过DeviceActivationService获取
        // 暂时返回空，实际实现需要调用相应的服务方法
        return new Result<DeviceActivationEntity>().ok(null);
    }
    
    @GetMapping("/mac/{macAddress}")
    @Operation(
        operationId = "getActivationsByMac",
        summary = "根据MAC地址获取激活记录",
        description = "根据设备MAC地址查询激活记录列表"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "设备不存在")
    })
    @RequiresPermission(value = "device:activation:view", description = "查看设备激活记录")
    public Result<List<DeviceActivationEntity>> getActivationsByMac(
            @Parameter(description = "设备MAC地址", required = true, example = "AA:BB:CC:DD:EE:FF")
            @PathVariable String macAddress) {
        // 这里需要通过DeviceActivationService获取
        // 暂时返回空列表，实际实现需要调用相应的服务方法
        return new Result<List<DeviceActivationEntity>>().ok(List.of());
    }
}
