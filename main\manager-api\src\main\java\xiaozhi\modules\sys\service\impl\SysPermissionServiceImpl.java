package xiaozhi.modules.sys.service.impl;

import java.util.List;
import java.util.Set;

import org.apache.shiro.util.CollectionUtils;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.sys.dao.SysPermissionDao;
import xiaozhi.modules.sys.entity.SysPermissionEntity;
import xiaozhi.modules.sys.service.SysPermissionService;

/**
 * 权限Service实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class SysPermissionServiceImpl extends BaseServiceImpl<SysPermissionDao, SysPermissionEntity> implements SysPermissionService {
    
    private final SysPermissionDao sysPermissionDao;
    
    @Override
    public Set<String> getUserPermissions(Long userId) {
        return sysPermissionDao.getUserPermissions(userId);
    }
    
    @Override
    public List<SysPermissionEntity> getPermissionsByRoleId(Long roleId) {
        return sysPermissionDao.getPermissionsByRoleId(roleId);
    }
    
    @Override
    public List<SysPermissionEntity> getPermissionsByDomainAndAction(String domain, String action) {
        return sysPermissionDao.getPermissionsByDomainAndAction(domain, action);
    }
    
    @Override
    public boolean hasPermission(Long userId, String permission) {
        Set<String> userPermissions = getUserPermissions(userId);
        if (CollectionUtils.isEmpty(userPermissions)) {
            return false;
        }
        
        // 检查是否有完全匹配的权限
        if (userPermissions.contains(permission)) {
            return true;
        }
        
        // 检查是否有通配符权限
        for (String userPermission : userPermissions) {
            if (isPermissionMatched(userPermission, permission)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public boolean hasTenantPermission(Long userId, String permission, Long tenantId) {
        Set<String> userPermissions = getUserPermissions(userId);
        if (CollectionUtils.isEmpty(userPermissions)) {
            return false;
        }
        
        // 替换权限字符串中的租户占位符
        String resolvedPermission = permission.replace("tenant", String.valueOf(tenantId));
        
        // 检查是否有完全匹配的权限
        if (userPermissions.contains(resolvedPermission)) {
            return true;
        }
        
        // 检查是否有通配符权限
        String wildcardPermission = permission.replace("tenant", "*");
        if (userPermissions.contains(wildcardPermission)) {
            return true;
        }
        
        // 检查其他通配符权限
        for (String userPermission : userPermissions) {
            if (isPermissionMatched(userPermission, resolvedPermission)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查权限是否匹配（支持通配符）
     * 
     * @param userPermission 用户权限
     * @param targetPermission 目标权限
     * @return 是否匹配
     */
    private boolean isPermissionMatched(String userPermission, String targetPermission) {
        if (userPermission.equals("*:*:*")) {
            return true; // 超级权限
        }
        
        String[] userParts = userPermission.split(":");
        String[] targetParts = targetPermission.split(":");
        
        if (userParts.length != targetParts.length) {
            return false;
        }
        
        for (int i = 0; i < userParts.length; i++) {
            if (!"*".equals(userParts[i]) && !userParts[i].equals(targetParts[i])) {
                return false;
            }
        }
        
        return true;
    }
}
