package xiaozhi.modules.security.user;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;

import xiaozhi.common.user.UserDetail;

/**
 * Shiro工具类
 * Copyright (c) 人人开源 All rights reserved.
 * Website: https://www.renren.io
 */
public class SecurityUser {

    public static Subject getSubject() {
        try {
            return SecurityUtils.getSubject();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取用户信息
     */
    public static UserDetail getUser() {
        Subject subject = getSubject();
        if (subject == null) {
            return new UserDetail();
        }

        UserDetail user = (UserDetail) subject.getPrincipal();
        if (user == null) {
            return new UserDetail();
        }

        return user;
    }

    public static String getToken() {
        return getUser().getToken();
    }

    /**
     * 获取用户ID
     */
    public static Long getUserId() {
        return getUser().getId();
    }

    /**
     * 获取租户ID
     */
    public static Long getTenantId() {
        return getUser().getTenantId();
    }

    /**
     * 获取用户类型
     */
    public static Integer getUserType() {
        return getUser().getUserType();
    }

    /**
     * 检查是否为平台管理员
     */
    public static boolean isPlatformAdmin() {
        UserDetail user = getUser();
        return user.getSuperAdmin() != null && user.getSuperAdmin() == 1;
    }

    /**
     * 检查是否为租户用户
     */
    public static boolean isTenantUser() {
        UserDetail user = getUser();
        return user.getUserType() != null && user.getUserType() == 2;
    }
}