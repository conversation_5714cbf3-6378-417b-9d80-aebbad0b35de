package xiaozhi.modules.sys.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.sys.dto.SysUserDTO;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.modules.sys.service.SysUserService;

/**
 * 租户用户管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/sys/tenant/user")
@Tag(name = "租户用户管理", description = "租户内用户的CRUD管理，包括用户创建、编辑、删除、角色分配等功能")
@AllArgsConstructor
public class SysTenantUserController {
    
    private final SysUserService sysUserService;
    
    @GetMapping("/page")
    @Operation(
        summary = "分页查询租户用户",
        description = "分页查询当前租户下的所有用户列表，支持按用户名、真实姓名等条件搜索"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "查看租户用户权限")
    public Result<PageData<SysUserDTO>> page(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") Integer limit,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "真实姓名") @RequestParam(required = false) String realName,
            @Parameter(description = "手机号") @RequestParam(required = false) String mobile,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Map<String, Object> params = Map.of(
            "page", page,
            "limit", limit,
            "username", username != null ? username : "",
            "realName", realName != null ? realName : "",
            "mobile", mobile != null ? mobile : "",
            "status", status != null ? status : "",
            "tenantId", SecurityUser.getTenantId()
        );
        
        PageData<SysUserDTO> pageData = sysUserService.page(params);
        return new Result<PageData<SysUserDTO>>().ok(pageData);
    }
    
    @GetMapping("/list")
    @Operation(
        summary = "获取租户用户列表",
        description = "获取当前租户下的所有用户列表，不分页"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "查看租户用户权限")
    public Result<List<SysUserDTO>> list() {
        QueryWrapper<SysUserEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("tenant_id", SecurityUser.getTenantId());
        wrapper.eq("deleted", 0);
        wrapper.orderByDesc("create_date");
        
        List<SysUserEntity> entityList = sysUserService.list(wrapper);
        List<SysUserDTO> dtoList = sysUserService.convertToDTO(entityList);
        
        return new Result<List<SysUserDTO>>().ok(dtoList);
    }
    
    @GetMapping("/{id}")
    @Operation(
        summary = "获取用户详情",
        description = "根据用户ID获取用户的详细信息，包括角色信息"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:view:tenant", tenant = true, description = "查看租户用户权限")
    public Result<SysUserDTO> info(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long id) {
        SysUserDTO user = sysUserService.get(id);
        
        // 验证用户是否属于当前租户
        if (user == null || !user.getTenantId().equals(SecurityUser.getTenantId())) {
            throw new RuntimeException("用户不存在或无权限访问");
        }
        
        return new Result<SysUserDTO>().ok(user);
    }
    
    @PostMapping
    @Operation(
        summary = "创建租户用户",
        description = "在当前租户下创建新用户，需要租户管理员权限"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数验证失败")
    })
    @RequiresPermission(value = "user:create:tenant", tenant = true, description = "创建租户用户权限")
    public Result<Void> save(@RequestBody SysUserDTO dto) {
        // 参数验证
        ValidatorUtils.validateEntity(dto);
        
        // 设置租户ID
        dto.setTenantId(SecurityUser.getTenantId());
        dto.setUserType(2); // 租户用户
        
        sysUserService.save(dto);
        
        return new Result<>();
    }
    
    @PutMapping
    @Operation(
        summary = "更新租户用户",
        description = "更新租户用户信息，只能更新当前租户下的用户"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:tenant", tenant = true, description = "编辑租户用户权限")
    public Result<Void> update(@RequestBody SysUserDTO dto) {
        // 参数验证
        ValidatorUtils.validateEntity(dto);
        
        // 验证用户是否属于当前租户
        SysUserDTO existingUser = sysUserService.get(dto.getId());
        if (existingUser == null || !existingUser.getTenantId().equals(SecurityUser.getTenantId())) {
            throw new RuntimeException("用户不存在或无权限访问");
        }
        
        // 保持租户ID不变
        dto.setTenantId(SecurityUser.getTenantId());
        
        sysUserService.update(dto);
        
        return new Result<>();
    }
    
    @DeleteMapping
    @Operation(
        summary = "删除租户用户",
        description = "批量删除租户用户，只能删除当前租户下的用户"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @RequiresPermission(value = "user:delete:tenant", tenant = true, description = "删除租户用户权限")
    public Result<Void> delete(@RequestBody Long[] ids) {
        // 验证所有用户都属于当前租户
        for (Long id : ids) {
            SysUserDTO user = sysUserService.get(id);
            if (user == null || !user.getTenantId().equals(SecurityUser.getTenantId())) {
                throw new RuntimeException("用户不存在或无权限删除");
            }
        }
        
        sysUserService.deleteBatch(ids);
        
        return new Result<>();
    }
    
    @PutMapping("/{id}/status")
    @Operation(
        summary = "修改用户状态",
        description = "启用或禁用租户用户"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "修改成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:tenant", tenant = true, description = "编辑租户用户权限")
    public Result<Void> updateStatus(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long id,
            @Parameter(description = "状态", required = true, example = "1")
            @RequestParam Integer status) {
        
        // 验证用户是否属于当前租户
        SysUserDTO user = sysUserService.get(id);
        if (user == null || !user.getTenantId().equals(SecurityUser.getTenantId())) {
            throw new RuntimeException("用户不存在或无权限访问");
        }
        
        user.setStatus(status);
        sysUserService.update(user);
        
        return new Result<>();
    }
    
    @PutMapping("/{id}/reset-password")
    @Operation(
        summary = "重置用户密码",
        description = "重置租户用户密码为默认密码"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重置成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @RequiresPermission(value = "user:update:tenant", tenant = true, description = "编辑租户用户权限")
    public Result<Void> resetPassword(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long id) {
        
        // 验证用户是否属于当前租户
        SysUserDTO user = sysUserService.get(id);
        if (user == null || !user.getTenantId().equals(SecurityUser.getTenantId())) {
            throw new RuntimeException("用户不存在或无权限访问");
        }
        
        // 重置为默认密码
        sysUserService.changePasswordDirectly(id, "123456");
        
        return new Result<>();
    }
}
