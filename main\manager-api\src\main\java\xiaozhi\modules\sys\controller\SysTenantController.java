package xiaozhi.modules.sys.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.sys.service.SysTenantService;

/**
 * 租户管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/sys/tenant")
@Tag(name = "租户管理", description = "多租户系统的租户管理相关接口，包括租户查询、企业关联等功能")
@AllArgsConstructor
public class SysTenantController {
    
    private final SysTenantService sysTenantService;
    
    @GetMapping("/list")
    @Operation(
        summary = "获取租户列表",
        description = "获取系统中所有租户的列表信息，支持分页查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功",
            content = @Content(schema = @Schema(implementation = Result.class))),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看租户列表")
    public Result<List<SysTenantEntity>> list() {
        List<SysTenantEntity> list = sysTenantService.list();
        return new Result<List<SysTenantEntity>>().ok(list);
    }
    
    @GetMapping("/{id}")
    @Operation(
        summary = "获取租户详情",
        description = "根据租户ID获取租户的详细信息，包括租户配置、状态等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "租户不存在")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看租户详情")
    public Result<SysTenantEntity> info(
            @Parameter(description = "租户ID", required = true, example = "1")
            @PathVariable Long id) {
        SysTenantEntity tenant = sysTenantService.getById(id);
        return new Result<SysTenantEntity>().ok(tenant);
    }
    
    @GetMapping("/org/{orgId}")
    @Operation(
        summary = "根据企业ID获取租户列表",
        description = "获取指定企业下的所有租户列表，用于企业-租户关联查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "404", description = "企业不存在")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看企业租户")
    public Result<List<SysTenantEntity>> getTenantsByOrgId(
            @Parameter(description = "企业ID", required = true, example = "1")
            @PathVariable Long orgId) {
        List<SysTenantEntity> tenants = sysTenantService.getTenantsByOrgId(orgId);
        return new Result<List<SysTenantEntity>>().ok(tenants);
    }
    
    @GetMapping("/expiring/{days}")
    @Operation(
        summary = "获取即将到期的租户",
        description = "获取在指定天数内即将到期的租户列表，用于到期提醒和续费管理"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @RequiresPermission(value = "tenant:view:*", description = "查看到期租户")
    public Result<List<SysTenantEntity>> getExpiringTenants(
            @Parameter(description = "天数", required = true, example = "30",
                schema = @Schema(minimum = "1", maximum = "365"))
            @PathVariable Integer days) {
        List<SysTenantEntity> tenants = sysTenantService.getExpiringTenants(days);
        return new Result<List<SysTenantEntity>>().ok(tenants);
    }
}
