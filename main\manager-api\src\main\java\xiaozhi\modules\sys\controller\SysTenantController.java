package xiaozhi.modules.sys.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.sys.service.SysTenantService;

/**
 * 租户管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/sys/tenant")
@Tag(name = "租户管理")
@AllArgsConstructor
public class SysTenantController {
    
    private final SysTenantService sysTenantService;
    
    @GetMapping("/list")
    @Operation(summary = "获取租户列表")
    @RequiresPermission(value = "tenant:view:*", description = "查看租户列表")
    public Result<List<SysTenantEntity>> list() {
        List<SysTenantEntity> list = sysTenantService.list();
        return new Result<List<SysTenantEntity>>().ok(list);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取租户详情")
    @RequiresPermission(value = "tenant:view:*", description = "查看租户详情")
    public Result<SysTenantEntity> info(@PathVariable Long id) {
        SysTenantEntity tenant = sysTenantService.getById(id);
        return new Result<SysTenantEntity>().ok(tenant);
    }
    
    @GetMapping("/org/{orgId}")
    @Operation(summary = "根据企业ID获取租户列表")
    @RequiresPermission(value = "tenant:view:*", description = "查看企业租户")
    public Result<List<SysTenantEntity>> getTenantsByOrgId(@PathVariable Long orgId) {
        List<SysTenantEntity> tenants = sysTenantService.getTenantsByOrgId(orgId);
        return new Result<List<SysTenantEntity>>().ok(tenants);
    }
    
    @GetMapping("/expiring/{days}")
    @Operation(summary = "获取即将到期的租户")
    @RequiresPermission(value = "tenant:view:*", description = "查看到期租户")
    public Result<List<SysTenantEntity>> getExpiringTenants(@PathVariable Integer days) {
        List<SysTenantEntity> tenants = sysTenantService.getExpiringTenants(days);
        return new Result<List<SysTenantEntity>>().ok(tenants);
    }
}
