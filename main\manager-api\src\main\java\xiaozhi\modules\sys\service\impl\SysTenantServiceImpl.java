package xiaozhi.modules.sys.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.sys.dao.SysTenantDao;
import xiaozhi.modules.sys.entity.SysTenantEntity;
import xiaozhi.modules.sys.service.SysTenantService;

/**
 * 租户Service实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class SysTenantServiceImpl extends BaseServiceImpl<SysTenantDao, SysTenantEntity> implements SysTenantService {
    
    private final SysTenantDao sysTenantDao;
    
    @Override
    public SysTenantEntity getByTenantCode(String tenantCode) {
        return sysTenantDao.getByTenantCode(tenantCode);
    }
    
    @Override
    public List<SysTenantEntity> getTenantsByOrgId(Long orgId) {
        return sysTenantDao.getTenantsByOrgId(orgId);
    }
    
    @Override
    public List<SysTenantEntity> getExpiringTenants(Integer days) {
        return sysTenantDao.getExpiringTenants(days);
    }
}
