package xiaozhi.modules.sys.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.sys.dao.SysOrganizationDao;
import xiaozhi.modules.sys.entity.SysOrganizationEntity;
import xiaozhi.modules.sys.service.SysOrganizationService;

/**
 * 企业组织Service实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class SysOrganizationServiceImpl extends BaseServiceImpl<SysOrganizationDao, SysOrganizationEntity> implements SysOrganizationService {
    
    private final SysOrganizationDao sysOrganizationDao;
    
    @Override
    public SysOrganizationEntity getByOrgCode(String orgCode) {
        return sysOrganizationDao.getByOrgCode(orgCode);
    }
    
    @Override
    public List<SysOrganizationEntity> getOrganizationsByType(Integer orgType) {
        return sysOrganizationDao.getOrganizationsByType(orgType);
    }
    
    @Override
    public List<SysOrganizationEntity> getExpiringOrganizations(Integer days) {
        return sysOrganizationDao.getExpiringOrganizations(days);
    }
}
