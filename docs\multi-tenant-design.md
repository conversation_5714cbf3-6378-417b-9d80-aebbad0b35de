# 小智ESP32服务器多租户改造设计文档

## 1. 概述

### 1.1 改造目标
为满足企业入驻需求，对开源项目xiaozhi-esp32-server进行多租户改造，支持多个企业独立使用系统，同时保证数据隔离和权限控制。

### 1.2 设计原则
- **租户隔离**：每个企业作为独立租户，数据完全隔离
- **权限分级**：平台管理员 > 租户管理员 > 租户普通用户
- **快速上线**：客户中台和运营后台融为一个系统
- **数据安全**：基于RBAC的权限控制，确保数据访问安全

## 2. 租户规则约定

### 2.1 租户划分
- **租户定义**：按企业进行划分，每个企业分配一个租户
- **关系映射**：企业和租户是一对一的关系
- **数据隔离**：每个租户只能访问自己的数据

### 2.2 权限层级

#### 2.2.1 平台管理员（超级管理员）
- **权限范围**：系统所有功能
- **主要职责**：
  - 添加企业、租户
  - 为企业创建租户管理员账号
  - 为租户创建智能体
  - 系统全局配置和监控

#### 2.2.2 租户管理员
- **权限范围**：所属租户的全部数据和功能
- **主要职责**：
  - 管理租户内的用户账号
  - 设备激活、重置、禁用等管理操作
  - 到期续费管理
  - 报表统计查看
  - 智能体查看（不可编辑）

#### 2.2.3 租户普通用户
- **权限范围**：根据角色分配的特定功能
- **主要职责**：
  - 设备管理操作（根据权限）
  - 智能体查看（不可编辑）
  - 基础数据查看

### 2.3 数据过滤机制
- **智能体权限**：通过分配智能体权限实现数据过滤
- **租户隔离**：所有数据查询都基于租户ID进行过滤
- **权限继承**：下级权限不能超越上级权限范围

## 3. 数据库设计

### 3.1 分租户表设计

#### 3.1.1 需要分租户的核心表

| 表名 | 是否分租户 | 备注 | 是否新增 |
|------|------------|------|----------|
| ai_agent | 是 | 智能体配置 | 否 |
| ai_device | 是 | 设备信息 | 否 |
| sys_user | 是 | 用户 | 否 |

#### 3.1.2 不需要分租户的表

| 表名 | 是否分租户 | 备注 | 是否新增 |
|------|------------|------|----------|
| ai_agent_chat_audio | 否 | 智能体语音聊天历史 | 否 |
| ai_agent_chat_history | 否 | 智能体文本聊天历史 | 否 |
| ai_agent_template | 否 | 智能体模板 | 否 |
| ai_model_config | 否 | 模型配置 | 否 |
| ai_model_provider | 否 | 模型供应商 | 否 |
| ai_ota | 否 | OTA | 否 |
| ai_tts_voice | 否 | TTS音色配置 | 否 |
| ai_voiceprint | 否 | 声纹信息 | 否 |
| databasechangelog | 否 | 数据库更新日志 | 否 |
| databasechangeloglock | 否 | 数据库更新锁 | 否 |
| sys_dict_data | 否 | 系统字典数据 | 否 |
| sys_dict_type | 否 | 系统字典分类 | 否 |
| sys_params | 否 | 系统参数 | 否 |
| sys_user_token | 否 | 用户口令 | 否 |

### 3.2 新增表设计

#### 3.2.1 企业组织表 (sys_organization)

```sql
CREATE TABLE `sys_organization` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '企业ID',
    `org_code` VARCHAR(50) NOT NULL COMMENT '企业编码',
    `org_name` VARCHAR(100) NOT NULL COMMENT '企业名称',
    `org_type` TINYINT DEFAULT 1 COMMENT '企业类型：1-普通企业，2-代理商',
    `contact_person` VARCHAR(50) COMMENT '联系人',
    `contact_phone` VARCHAR(20) COMMENT '联系电话',
    `contact_email` VARCHAR(100) COMMENT '联系邮箱',
    `address` VARCHAR(200) COMMENT '企业地址',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `expire_date` DATETIME COMMENT '到期时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_org_code` (`org_code`),
    KEY `idx_org_name` (`org_name`),
    KEY `idx_expire_date` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业组织表';
```

#### 3.2.2 租户表 (sys_tenant)

```sql
CREATE TABLE `sys_tenant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '租户ID',
    `tenant_code` VARCHAR(50) NOT NULL COMMENT '租户编码',
    `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户名称',
    `org_id` BIGINT NOT NULL COMMENT '关联企业ID',
    `admin_user_id` BIGINT COMMENT '租户管理员用户ID',
    `max_users` INT DEFAULT 10 COMMENT '最大用户数',
    `max_devices` INT DEFAULT 100 COMMENT '最大设备数',
    `max_agents` INT DEFAULT 5 COMMENT '最大智能体数',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `expire_date` DATETIME COMMENT '到期时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_code`),
    KEY `idx_org_id` (`org_id`),
    KEY `idx_admin_user_id` (`admin_user_id`),
    KEY `idx_expire_date` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户表';
```

#### 3.2.3 权限表 (sys_permission) - 基于Shiro最佳实践

```sql
CREATE TABLE `sys_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `permission` VARCHAR(200) NOT NULL COMMENT '权限字符串（domain:action:instance）',
    `description` VARCHAR(200) NOT NULL COMMENT '权限描述',
    `domain` VARCHAR(50) NOT NULL COMMENT '权限域（资源类型）',
    `action` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `instance` VARCHAR(100) COMMENT '实例标识（可选，支持通配符*）',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission` (`permission`),
    KEY `idx_domain_action` (`domain`, `action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';
```

#### 3.2.4 菜单表 (sys_menu) - 前端菜单配置，与权限解耦

```sql
CREATE TABLE `sys_menu` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `menu_code` VARCHAR(50) NOT NULL COMMENT '菜单编码',
    `menu_name` VARCHAR(100) NOT NULL COMMENT '菜单名称',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父菜单ID',
    `path` VARCHAR(200) COMMENT '路由路径',
    `component` VARCHAR(200) COMMENT '组件路径',
    `icon` VARCHAR(50) COMMENT '图标',
    `sort` INT DEFAULT 0 COMMENT '排序',
    `visible` TINYINT DEFAULT 1 COMMENT '是否显示：0-隐藏，1-显示',
    `required_permission` VARCHAR(200) COMMENT '所需权限（对应权限表的permission字段）',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_menu_code` (`menu_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_required_permission` (`required_permission`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';
```

#### 3.2.5 角色表 (sys_role)

```sql
CREATE TABLE `sys_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
    `role_name` VARCHAR(100) NOT NULL COMMENT '角色名称',
    `role_type` TINYINT DEFAULT 1 COMMENT '角色类型：1-平台角色，2-租户角色',
    `tenant_id` BIGINT COMMENT '租户ID（租户角色必填）',
    `data_scope` TINYINT DEFAULT 1 COMMENT '数据范围：1-全部，2-本租户，3-本部门，4-仅本人',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code_tenant` (`role_code`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_role_type` (`role_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 3.2.6 角色权限关联表 (sys_role_permission)

```sql
CREATE TABLE `sys_role_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT NOT NULL COMMENT '权限ID',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

#### 3.2.7 用户角色关联表 (sys_user_role)

```sql
CREATE TABLE `sys_user_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

### 3.3 现有表改造

#### 3.3.1 用户表 (sys_user) 改造

```sql
-- 添加租户相关字段
ALTER TABLE `sys_user`
ADD COLUMN `tenant_id` BIGINT COMMENT '租户ID' AFTER `id`,
ADD COLUMN `user_type` TINYINT DEFAULT 2 COMMENT '用户类型：1-平台管理员，2-租户用户' AFTER `tenant_id`,
ADD INDEX `idx_tenant_id` (`tenant_id`);
```

#### 3.3.2 智能体表 (ai_agent) 改造

```sql
-- 添加租户相关字段
ALTER TABLE `ai_agent`
ADD COLUMN `tenant_id` BIGINT COMMENT '租户ID' AFTER `user_id`,
ADD INDEX `idx_tenant_id` (`tenant_id`);
```

#### 3.3.3 设备表 (ai_device) 改造

```sql
-- 添加租户相关字段
ALTER TABLE `ai_device`
ADD COLUMN `tenant_id` BIGINT COMMENT '租户ID' AFTER `user_id`,
ADD INDEX `idx_tenant_id` (`tenant_id`);
```

## 4. 权限设计

### 4.1 预设角色定义

#### 4.1.1 平台角色

| 角色编码 | 角色名称 | 角色类型 | 描述 |
|----------|----------|----------|------|
| PLATFORM_ADMIN | 平台管理员 | 平台角色 | 系统超级管理员，拥有所有权限 |
| PLATFORM_OPERATOR | 平台运营 | 平台角色 | 平台运营人员，负责企业管理 |

#### 4.1.2 租户角色

| 角色编码 | 角色名称 | 角色类型 | 描述 |
|----------|----------|----------|------|
| TENANT_ADMIN | 租户管理员 | 租户角色 | 租户超级管理员，管理租户内所有资源 |
| TENANT_DEVICE_MANAGER | 设备管理员 | 租户角色 | 负责设备激活、重置、禁用等操作 |
| TENANT_OPERATOR | 租户运营 | 租户角色 | 负责报表统计、数据分析 |
| TENANT_USER | 普通用户 | 租户角色 | 基础用户，只能查看分配的资源 |

### 4.2 权限设计（基于Shiro最佳实践）

#### 4.2.1 权限字符串格式

采用Shiro标准的权限字符串格式：`domain:action:instance`

- **domain**：权限域，表示资源类型（如：organization、tenant、user、agent、device）
- **action**：操作类型（如：view、create、update、delete、bind、unbind）
- **instance**：实例标识，可选（如：具体的资源ID，支持通配符*表示所有）

#### 4.2.2 权限定义

##### 4.2.2.1 企业管理权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| organization:view:* | 查看所有企业 | PLATFORM_ADMIN, PLATFORM_OPERATOR |
| organization:create:* | 创建企业 | PLATFORM_ADMIN |
| organization:update:* | 编辑企业 | PLATFORM_ADMIN |
| organization:delete:* | 删除企业 | PLATFORM_ADMIN |

##### 4.2.2.2 租户管理权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| tenant:view:* | 查看所有租户 | PLATFORM_ADMIN, PLATFORM_OPERATOR |
| tenant:create:* | 创建租户 | PLATFORM_ADMIN |
| tenant:update:* | 编辑租户 | PLATFORM_ADMIN |
| tenant:delete:* | 删除租户 | PLATFORM_ADMIN |

##### 4.2.2.3 用户管理权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| user:view:* | 查看所有用户 | PLATFORM_ADMIN |
| user:view:tenant | 查看租户内用户 | TENANT_ADMIN |
| user:create:* | 创建任意用户 | PLATFORM_ADMIN |
| user:create:tenant | 创建租户用户 | TENANT_ADMIN |
| user:update:* | 编辑任意用户 | PLATFORM_ADMIN |
| user:update:tenant | 编辑租户用户 | TENANT_ADMIN |
| user:delete:* | 删除任意用户 | PLATFORM_ADMIN |
| user:delete:tenant | 删除租户用户 | TENANT_ADMIN |

##### 4.2.2.4 角色管理权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| role:view:* | 查看所有角色 | PLATFORM_ADMIN |
| role:view:tenant | 查看租户角色 | TENANT_ADMIN |
| role:create:platform | 创建平台角色 | PLATFORM_ADMIN |
| role:create:tenant | 创建租户角色 | TENANT_ADMIN |
| role:update:* | 编辑任意角色 | PLATFORM_ADMIN |
| role:update:tenant | 编辑租户角色 | TENANT_ADMIN |
| role:delete:* | 删除任意角色 | PLATFORM_ADMIN |
| role:delete:tenant | 删除租户角色 | TENANT_ADMIN |

##### 4.2.2.5 智能体管理权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| agent:view:* | 查看所有智能体 | PLATFORM_ADMIN |
| agent:view:tenant | 查看租户智能体 | 租户所有角色 |
| agent:create:* | 创建智能体 | PLATFORM_ADMIN |
| agent:update:* | 编辑智能体 | PLATFORM_ADMIN |
| agent:delete:* | 删除智能体 | PLATFORM_ADMIN |
| agent:assign:tenant | 分配智能体给租户 | PLATFORM_ADMIN |

##### 4.2.2.6 设备管理权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| device:view:* | 查看所有设备 | PLATFORM_ADMIN |
| device:view:tenant | 查看租户设备 | 租户所有角色 |
| device:bind:tenant | 绑定设备 | TENANT_ADMIN, TENANT_DEVICE_MANAGER |
| device:unbind:tenant | 解绑设备 | TENANT_ADMIN, TENANT_DEVICE_MANAGER |
| device:reset:tenant | 重置设备 | TENANT_ADMIN, TENANT_DEVICE_MANAGER |
| device:disable:tenant | 禁用设备 | TENANT_ADMIN, TENANT_DEVICE_MANAGER |

##### 4.2.2.7 报表统计权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| report:view:* | 查看所有报表 | PLATFORM_ADMIN |
| report:view:tenant | 查看租户报表 | TENANT_ADMIN, TENANT_OPERATOR |
| report:export:tenant | 导出租户报表 | TENANT_ADMIN, TENANT_OPERATOR |

##### 4.2.2.8 系统配置权限

| 权限字符串 | 描述 | 适用角色 |
|------------|------|----------|
| system:config:* | 系统配置管理 | PLATFORM_ADMIN |
| system:log:view | 查看系统日志 | PLATFORM_ADMIN |

### 4.3 权限校验机制（基于Shiro）

#### 4.3.1 权限校验注解

```java
// 方法级权限校验
@RequiresPermissions("device:view:tenant")
public List<Device> getDeviceList(DeviceQuery query) {
    // 业务逻辑
}

// 多权限校验（AND关系）
@RequiresPermissions({"device:view:tenant", "device:bind:tenant"})
public void bindDevice(String deviceId, String agentId) {
    // 业务逻辑
}

// 多权限校验（OR关系）
@RequiresPermissions(value = {"device:update:tenant", "device:delete:tenant"}, logical = Logical.OR)
public void manageDevice(String deviceId) {
    // 业务逻辑
}
```

#### 4.3.2 编程式权限校验

```java
@Service
public class DeviceService {

    public List<Device> getDeviceList(DeviceQuery query) {
        Subject subject = SecurityUtils.getSubject();

        // 检查权限
        if (subject.isPermitted("device:view:*")) {
            // 平台管理员，查看所有设备
            return deviceMapper.selectAll(query);
        } else if (subject.isPermitted("device:view:tenant")) {
            // 租户用户，只查看租户设备
            query.setTenantId(getCurrentTenantId());
            return deviceMapper.selectByTenant(query);
        } else {
            throw new UnauthorizedException("无权限访问设备信息");
        }
    }

    public void deleteDevice(String deviceId) {
        Subject subject = SecurityUtils.getSubject();

        // 动态权限校验
        String permission = "device:delete:" + deviceId;
        if (!subject.isPermitted(permission)) {
            throw new UnauthorizedException("无权限删除该设备");
        }

        deviceMapper.deleteById(deviceId);
    }
}
```

#### 4.3.3 数据范围控制

| 权限实例 | 数据范围 | 说明 |
|----------|----------|------|
| * | 全部数据 | 平台管理员可访问所有数据 |
| tenant | 租户数据 | 只能访问所属租户的数据 |
| {tenantId} | 特定租户 | 访问指定租户的数据 |
| {resourceId} | 特定资源 | 访问指定资源实例 |

#### 4.3.4 自定义权限解析器

```java
@Component
public class CustomPermissionResolver implements PermissionResolver {

    @Override
    public Permission resolvePermission(String permissionString) {
        if (permissionString.contains(":")) {
            return new WildcardPermission(permissionString);
        }
        return new WildcardPermission(permissionString);
    }
}

@Component
public class TenantPermissionResolver {

    public boolean isPermitted(String permission, Long tenantId) {
        Subject subject = SecurityUtils.getSubject();

        // 替换权限字符串中的租户占位符
        String resolvedPermission = permission.replace("tenant", String.valueOf(tenantId));

        return subject.isPermitted(resolvedPermission) ||
               subject.isPermitted(permission.replace("tenant", "*"));
    }
}
```

## 5. 系统架构设计

### 5.1 多租户架构模式

采用**共享数据库、共享Schema**的多租户架构模式：

- **优点**：成本低、维护简单、扩展性好
- **缺点**：数据隔离依赖应用层控制
- **适用场景**：中小型企业SaaS平台

### 5.2 租户识别机制

#### 5.2.1 请求头识别
```http
X-Tenant-Id: tenant_001
Authorization: Bearer <token>
```

#### 5.2.2 Token中包含租户信息
```json
{
  "userId": 1001,
  "tenantId": 1,
  "userType": 2,
  "roles": ["TENANT_ADMIN"]
}
```

#### 5.2.3 域名识别（可选）
```
tenant1.xiaozhi.com -> tenant_id: 1
tenant2.xiaozhi.com -> tenant_id: 2
```

### 5.3 数据隔离策略

#### 5.3.1 应用层隔离
- 所有数据查询自动添加租户过滤条件
- 使用MyBatis拦截器实现自动租户过滤
- 在Service层进行权限校验

#### 5.3.2 数据库层隔离
- 通过tenant_id字段进行逻辑隔离
- 创建复合索引提高查询性能
- 定期清理删除标记的数据

## 6. 实施计划

### 6.1 第一阶段：基础架构搭建

#### 6.1.1 数据库改造（1周）
- [ ] 创建新增表结构
- [ ] 修改现有表结构
- [ ] 初始化基础数据
- [ ] 数据迁移脚本

#### 6.1.2 权限框架开发（2周）
- [x] RBAC权限框架
- [x] 租户过滤拦截器
- [x] 数据权限注解
- [x] 权限校验工具类

### 6.2 第二阶段：业务功能改造

#### 6.2.1 用户管理改造（1周）
- [ ] 用户注册/登录
- [ ] 租户用户管理
- [ ] 角色权限分配
- [ ] 用户权限校验

#### 6.2.2 企业租户管理（1周）
- [ ] 企业管理功能
- [ ] 租户管理功能
- [ ] 租户配额管理
- [ ] 到期时间管理

#### 6.2.3 智能体管理改造（1周）
- [ ] 智能体租户隔离
- [ ] 智能体权限控制
- [ ] 智能体分配管理
- [ ] 智能体使用统计

#### 6.2.4 设备管理改造（1周）
- [ ] 设备租户隔离
- [ ] 设备权限控制
- [ ] 设备激活流程
- [ ] 设备使用统计

### 6.3 第三阶段：前端界面改造

#### 6.3.1 管理后台改造（2周）
- [ ] 企业管理界面
- [ ] 租户管理界面
- [ ] 用户管理界面
- [ ] 权限管理界面

#### 6.3.2 租户界面开发（2周）
- [ ] 租户登录界面
- [ ] 租户仪表板
- [ ] 设备管理界面
- [ ] 报表统计界面

### 6.4 第四阶段：测试与上线

#### 6.4.1 功能测试（1周）
- [ ] 单元测试
- [ ] 集成测试
- [ ] 权限测试
- [ ] 性能测试

#### 6.4.2 部署上线（1周）
- [ ] 生产环境部署
- [ ] 数据迁移
- [ ] 监控配置
- [ ] 文档完善

## 7. 风险控制

### 7.1 数据安全风险

#### 7.1.1 风险点
- 租户数据泄露
- 权限越权访问
- 数据误删除

#### 7.1.2 控制措施
- 多层权限校验
- 数据访问日志
- 定期数据备份
- 软删除机制

### 7.2 性能风险

#### 7.2.1 风险点
- 大量租户并发访问
- 数据库查询性能下降
- 系统响应时间增加

#### 7.2.2 控制措施
- 数据库索引优化
- 查询缓存机制
- 分页查询限制
- 监控告警机制

### 7.3 业务风险

#### 7.3.1 风险点
- 租户配额超限
- 服务到期中断
- 数据迁移失败

#### 7.3.2 控制措施
- 配额监控告警
- 到期提醒机制
- 数据备份恢复
- 灰度发布策略

## 8. 总结

本设计文档详细规划了xiaozhi-esp32-server的多租户改造方案，通过引入企业组织、租户、权限等概念，实现了数据隔离和权限控制。采用共享数据库架构，在保证成本控制的同时，提供了良好的扩展性和维护性。

改造完成后，系统将支持：
- 多企业独立使用
- 分级权限管理
- 数据完全隔离
- 灵活的角色配置
- 完善的监控统计

为企业级SaaS服务奠定了坚实的技术基础。
