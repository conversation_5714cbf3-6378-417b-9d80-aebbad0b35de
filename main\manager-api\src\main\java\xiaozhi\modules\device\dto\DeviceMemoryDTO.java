package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 设备记忆DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "设备记忆DTO")
public class DeviceMemoryDTO {

    @NotBlank(message = "记忆内容不能为空")
    @Schema(description = "设备记忆内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "用户喜欢听音乐，经常询问天气信息")
    private String summaryMemory;
}
