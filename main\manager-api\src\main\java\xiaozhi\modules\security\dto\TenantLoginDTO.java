package xiaozhi.modules.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 租户登录DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "租户登录请求")
public class TenantLoginDTO {
    
    @Schema(description = "用户名", required = true, example = "admin")
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @Schema(description = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Schema(description = "租户编码", example = "TENANT001")
    private String tenantCode;
    
    @Schema(description = "验证码ID", example = "uuid-1234")
    private String captchaId;
    
    @Schema(description = "验证码", example = "abcd")
    private String captcha;
    
    @Schema(description = "手机验证码", example = "123456")
    private String mobileCaptcha;
    
    @Schema(description = "记住我", example = "true")
    private Boolean rememberMe = false;
}
