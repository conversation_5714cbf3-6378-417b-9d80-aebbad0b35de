package xiaozhi.modules.security.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;

import jakarta.annotation.PostConstruct;
import xiaozhi.modules.security.tenant.TenantInterceptor;

/**
 * 租户配置
 *
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Configuration
public class TenantConfig {

    @Autowired(required = false)
    private MybatisPlusInterceptor mybatisPlusInterceptor;

    /**
     * 添加租户拦截器到现有的MyBatis Plus拦截器
     */
    @PostConstruct
    public void addTenantInterceptor() {
        if (mybatisPlusInterceptor != null) {
            // 在第一个位置添加租户拦截器，确保优先执行
            mybatisPlusInterceptor.getInterceptors().add(0, new TenantInterceptor());
        }
    }
}
