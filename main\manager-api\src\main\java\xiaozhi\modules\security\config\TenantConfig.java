package xiaozhi.modules.security.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

import xiaozhi.modules.security.tenant.TenantInterceptor;

/**
 * 租户配置
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Configuration
public class TenantConfig {
    
    /**
     * 配置MyBatis Plus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加租户拦截器
        interceptor.addInnerInterceptor(new TenantInterceptor());
        
        // 添加分页拦截器
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        
        return interceptor;
    }
}
