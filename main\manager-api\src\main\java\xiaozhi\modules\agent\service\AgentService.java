package xiaozhi.modules.agent.service;

import java.util.List;
import java.util.Map;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.agent.dto.AgentAssignmentQueryDTO;
import xiaozhi.modules.agent.dto.AgentAssignmentRequestDTO;
import xiaozhi.modules.agent.dto.AgentDTO;
import xiaozhi.modules.agent.dto.AgentStatisticsQueryDTO;
import xiaozhi.modules.agent.dto.AgentStatisticsResponseDTO;
import xiaozhi.modules.agent.entity.AgentAssignmentEntity;
import xiaozhi.modules.agent.entity.AgentEntity;

/**
 * 智能体表处理service
 *
 * <AUTHOR>
 * @version 1.0, 2025/4/30
 * @since 1.0.0
 */
public interface AgentService extends BaseService<AgentEntity> {
    /**
     * 获取管理员智能体列表
     *
     * @param params 查询参数
     * @return 分页数据
     */
    PageData<AgentEntity> adminAgentList(Map<String, Object> params);

    /**
     * 根据ID获取智能体
     *
     * @param id 智能体ID
     * @return 智能体实体
     */
    AgentEntity getAgentById(String id);

    /**
     * 插入智能体
     *
     * @param entity 智能体实体
     * @return 是否成功
     */
    boolean insert(AgentEntity entity);

    /**
     * 根据用户ID删除智能体
     *
     * @param userId 用户ID
     */
    void deleteAgentByUserId(Long userId);

    /**
     * 获取用户智能体列表
     *
     * @param userId 用户ID
     * @return 智能体列表
     */
    List<AgentDTO> getUserAgents(Long userId);

    /**
     * 根据智能体ID获取设备数量
     *
     * @param agentId 智能体ID
     * @return 设备数量
     */
    Integer getDeviceCountByAgentId(String agentId);

    /**
     * 根据设备MAC地址查询对应设备的默认智能体信息
     *
     * @param macAddress 设备MAC地址
     * @return 默认智能体信息，不存在时返回null
     */
    AgentEntity getDefaultAgentByMacAddress(String macAddress);

    /**
     * 检查用户是否有权限访问智能体
     *
     * @param agentId 智能体ID
     * @param userId  用户ID
     * @return 是否有权限
     */
    boolean checkAgentPermission(String agentId, Long userId);

    /**
     * 分配智能体给用户
     *
     * @param assignmentRequest 分配请求
     */
    void assignAgentsToUsers(AgentAssignmentRequestDTO assignmentRequest);

    /**
     * 取消智能体分配
     *
     * @param assignmentRequest 取消分配请求
     */
    void unassignAgentsFromUsers(AgentAssignmentRequestDTO assignmentRequest);

    /**
     * 分页查询智能体分配记录
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    PageData<AgentAssignmentEntity> pageAssignments(AgentAssignmentQueryDTO queryDTO);

    /**
     * 获取用户已分配的智能体列表
     *
     * @param userId 用户ID
     * @return 智能体列表
     */
    List<AgentDTO> getAssignedAgentsByUserId(Long userId);

    /**
     * 获取智能体已分配的用户列表
     *
     * @param agentId 智能体ID
     * @return 用户ID列表
     */
    List<Long> getAssignedUsersByAgentId(String agentId);

    /**
     * 获取智能体使用统计
     *
     * @param queryDTO 查询条件
     * @return 统计数据
     */
    AgentStatisticsResponseDTO getAgentStatistics(AgentStatisticsQueryDTO queryDTO);

    /**
     * 获取热门智能体排行
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行列表
     */
    List<Map<String, Object>> getPopularAgentsRanking(String startDate, String endDate, Integer limit);

    /**
     * 记录智能体使用情况
     *
     * @param agentId 智能体ID
     * @param userId 用户ID
     * @param duration 使用时长
     * @param isSuccess 是否成功
     */
    void recordAgentUsage(String agentId, Long userId, Long duration, Boolean isSuccess);

    /**
     * 获取租户智能体统计概览
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    Map<String, Object> getTenantAgentOverview(Long tenantId);
}
