package xiaozhi.modules.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 租户用户注册DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@Data
@Schema(description = "租户用户注册请求")
public class TenantUserRegisterDTO {
    
    @Schema(description = "用户名", required = true, example = "admin")
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @Schema(description = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Schema(description = "确认密码", required = true, example = "123456")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;
    
    @Schema(description = "真实姓名", example = "张三")
    private String realName;
    
    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    @Pattern(regexp = "^[A-Za-z0-9+_.-]+@(.+)$", message = "邮箱格式不正确")
    private String email;
    
    @Schema(description = "租户编码", required = true, example = "TENANT001")
    @NotBlank(message = "租户编码不能为空")
    private String tenantCode;
    
    @Schema(description = "验证码ID", example = "uuid-1234")
    private String captchaId;
    
    @Schema(description = "验证码", example = "abcd")
    private String captcha;
    
    @Schema(description = "手机验证码", example = "123456")
    private String mobileCaptcha;
    
    @Schema(description = "邀请码", example = "INVITE123")
    private String inviteCode;
}
