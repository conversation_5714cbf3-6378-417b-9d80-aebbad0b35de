package xiaozhi.modules.sys.service;

import java.util.List;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.entity.SysOrganizationEntity;

/**
 * 企业组织Service
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface SysOrganizationService extends BaseService<SysOrganizationEntity> {
    
    /**
     * 根据企业编码获取企业信息
     * 
     * @param orgCode 企业编码
     * @return 企业实体
     */
    SysOrganizationEntity getByOrgCode(String orgCode);
    
    /**
     * 根据企业类型获取企业列表
     * 
     * @param orgType 企业类型
     * @return 企业实体列表
     */
    List<SysOrganizationEntity> getOrganizationsByType(Integer orgType);
    
    /**
     * 获取即将到期的企业列表
     * 
     * @param days 天数
     * @return 企业实体列表
     */
    List<SysOrganizationEntity> getExpiringOrganizations(Integer days);
}
