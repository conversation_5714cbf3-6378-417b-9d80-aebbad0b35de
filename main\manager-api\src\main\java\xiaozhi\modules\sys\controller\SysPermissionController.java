package xiaozhi.modules.sys.controller;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.security.annotation.RequiresPermission;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.security.utils.PermissionUtils;
import xiaozhi.modules.sys.entity.SysPermissionEntity;
import xiaozhi.modules.sys.service.SysPermissionService;

/**
 * 权限管理Controller
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/sys/permission")
@Tag(name = "权限管理")
@AllArgsConstructor
public class SysPermissionController {
    
    private final SysPermissionService sysPermissionService;
    private final PermissionUtils permissionUtils;
    
    @GetMapping("/user/permissions")
    @Operation(summary = "获取当前用户权限列表")
    public Result<Set<String>> getUserPermissions() {
        Long userId = SecurityUser.getUserId();
        Set<String> permissions = sysPermissionService.getUserPermissions(userId);
        return new Result<Set<String>>().ok(permissions);
    }
    
    @GetMapping("/role/{roleId}")
    @Operation(summary = "获取角色权限列表")
    @RequiresPermission(value = "role:view:*", description = "查看角色权限")
    public Result<List<SysPermissionEntity>> getRolePermissions(@PathVariable Long roleId) {
        List<SysPermissionEntity> permissions = sysPermissionService.getPermissionsByRoleId(roleId);
        return new Result<List<SysPermissionEntity>>().ok(permissions);
    }
    
    @GetMapping("/domain/{domain}/action/{action}")
    @Operation(summary = "根据权限域和操作获取权限列表")
    @RequiresPermission(value = "system:config:*", description = "系统配置权限")
    public Result<List<SysPermissionEntity>> getPermissionsByDomainAndAction(
            @PathVariable String domain, 
            @PathVariable String action) {
        List<SysPermissionEntity> permissions = sysPermissionService.getPermissionsByDomainAndAction(domain, action);
        return new Result<List<SysPermissionEntity>>().ok(permissions);
    }
    
    @GetMapping("/check/{permission}")
    @Operation(summary = "检查当前用户是否有指定权限")
    public Result<Boolean> checkPermission(@PathVariable String permission) {
        boolean hasPermission = permissionUtils.hasPermission(permission);
        return new Result<Boolean>().ok(hasPermission);
    }
    
    @GetMapping("/check/tenant/{permission}")
    @Operation(summary = "检查当前用户是否有租户权限")
    public Result<Boolean> checkTenantPermission(@PathVariable String permission) {
        boolean hasPermission = permissionUtils.hasTenantPermission(permission);
        return new Result<Boolean>().ok(hasPermission);
    }
    
    @GetMapping("/user/info")
    @Operation(summary = "获取当前用户权限信息")
    public Result<Object> getUserInfo() {
        return new Result<>().ok(Map.of(
            "userId", SecurityUser.getUserId(),
            "tenantId", SecurityUser.getTenantId(),
            "userType", SecurityUser.getUserType(),
            "isPlatformAdmin", permissionUtils.isPlatformAdmin(),
            "isTenantAdmin", permissionUtils.isTenantAdmin(),
            "permissions", permissionUtils.getUserPermissions()
        ));
    }
}
