package xiaozhi.modules.device.service;

import java.util.Date;
import java.util.List;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.device.dto.*;
import xiaozhi.modules.device.entity.DeviceActivationEntity;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.vo.UserShowDeviceListVO;

public interface DeviceService extends BaseService<DeviceEntity> {

    /**
     * 检查设备是否激活
     */
    DeviceReportRespDTO checkDeviceActive(String macAddress, String clientId,
            DeviceReportReqDTO deviceReport);

    /**
     * 获取用户指定智能体的设备列表，
     */
    List<DeviceEntity> getUserDevices(Long userId, String agentId);

    /**
     * 解绑设备
     */
    void unbindDevice(Long userId, String deviceId);

    /**
     * 设备激活
     */
    Boolean deviceActivation(String agentId, String activationCode);

    /**
     * 删除此用户的所有设备
     * 
     * @param userId 用户id
     */
    void deleteByUserId(Long userId);

    /**
     * 删除指定智能体关联的所有设备
     * 
     * @param agentId 智能体id
     */
    void deleteByAgentId(String agentId);

    /**
     * 获取指定用户的设备数量
     * 
     * @param userId 用户id
     * @return 设备数量
     */
    Long selectCountByUserId(Long userId);

    /**
     * 分页获取全部设备信息
     *
     * @param dto 分页查找参数
     * @return 用户列表分页数据
     */
    PageData<UserShowDeviceListVO> page(DevicePageUserDTO dto);

    /**
     * 根据MAC地址获取设备信息
     * 
     * @param macAddress MAC地址
     * @return 设备信息
     */
    DeviceEntity getDeviceByMacAddress(String macAddress);

    /**
     * 根据设备ID获取激活码
     * 
     * @param deviceId 设备ID
     * @return 激活码
     */
    String geCodeByDeviceId(String deviceId);

    /**
     * 获取这个智能体设备理的最近的最后连接时间
     * @param agentId 智能体id
     * @return 返回设备最近的最后连接时间
     */
    Date getLatestLastConnectionTime(String agentId);

    /**
     * 保存或更新设备记忆
     *
     * @param macAddress MAC地址
     * @param dto 记忆数据
     * @return 响应结果
     */
    DeviceMemoryResponseDTO saveDeviceMemory(String macAddress, DeviceMemoryDTO dto);

    /**
     * 获取设备记忆
     *
     * @param macAddress MAC地址
     * @return 记忆数据
     */
    DeviceMemoryResponseDTO getDeviceMemory(String macAddress);

    /**
     * 分页查询设备激活记录
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    PageData<DeviceActivationEntity> pageActivations(DeviceActivationQueryDTO queryDTO);

    /**
     * 获取设备使用统计
     *
     * @param queryDTO 查询条件
     * @return 统计数据
     */
    DeviceStatisticsResponseDTO getDeviceStatistics(DeviceStatisticsQueryDTO queryDTO);

    /**
     * 获取活跃设备排行
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行列表
     */
    List<Map<String, Object>> getActiveDevicesRanking(String startDate, String endDate, Integer limit);

    /**
     * 记录设备使用情况
     *
     * @param deviceId 设备ID
     * @param macAddress MAC地址
     * @param onlineDuration 在线时长
     * @param isSuccess 是否成功连接
     * @param dataTransferBytes 数据传输量
     */
    void recordDeviceUsage(String deviceId, String macAddress, Long onlineDuration, Boolean isSuccess, Long dataTransferBytes);

    /**
     * 获取租户设备统计概览
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    Map<String, Object> getTenantDeviceOverview(Long tenantId);

    /**
     * 检查设备权限
     *
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean checkDevicePermission(String deviceId, Long userId);

    /**
     * 获取用户设备列表（租户隔离）
     *
     * @param userId 用户ID
     * @return 设备列表
     */
    List<DeviceEntity> getUserDevicesWithTenant(Long userId);

    /**
     * 创建设备激活记录
     *
     * @param macAddress MAC地址
     * @param agentId 智能体ID
     * @param activationCode 激活码
     * @return 激活记录
     */
    DeviceActivationEntity createActivationRecord(String macAddress, String agentId, String activationCode);

    /**
     * 更新设备激活状态
     *
     * @param activationCode 激活码
     * @param status 激活状态
     * @param deviceId 设备ID
     * @param failureReason 失败原因
     */
    void updateActivationStatus(String activationCode, Integer status, String deviceId, String failureReason);

}