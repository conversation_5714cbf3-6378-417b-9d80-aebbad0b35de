package xiaozhi.modules.sys.service;

import java.util.List;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.sys.entity.SysTenantEntity;

/**
 * 租户Service
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface SysTenantService extends BaseService<SysTenantEntity> {
    
    /**
     * 根据租户编码获取租户信息
     * 
     * @param tenantCode 租户编码
     * @return 租户实体
     */
    SysTenantEntity getByTenantCode(String tenantCode);
    
    /**
     * 根据企业ID获取租户列表
     * 
     * @param orgId 企业ID
     * @return 租户实体列表
     */
    List<SysTenantEntity> getTenantsByOrgId(Long orgId);
    
    /**
     * 获取即将到期的租户列表
     * 
     * @param days 天数
     * @return 租户实体列表
     */
    List<SysTenantEntity> getExpiringTenants(Integer days);
}
